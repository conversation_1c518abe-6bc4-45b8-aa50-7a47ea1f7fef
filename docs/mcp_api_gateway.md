# MCP Server API Gateway

The MCP Server API Gateway provides a secure, rate-limited proxy for accessing purchased MCP servers through the Gumroad marketplace.

## Overview

The gateway handles:
- API key authentication
- Rate limiting per API key and globally per IP
- Request proxying to MCP servers
- Usage tracking and billing
- Health monitoring
- Error handling and logging

## Authentication

All requests to MCP servers must include a valid API key obtained after purchasing an MCP server.

### API Key Methods

1. **Authorization Header (Recommended)**
   ```
   Authorization: Bearer your_api_key_here
   ```

2. **Custom Header**
   ```
   X-API-Key: your_api_key_here
   ```

3. **Query Parameter**
   ```
   ?api_key=your_api_key_here
   ```

## Endpoints

### Proxy Requests

All MCP server requests are proxied through the gateway:

```
https://gumroad.com/api/mcp/{server_id}/{path}
```

- `{server_id}`: The external ID of the MCP server
- `{path}`: The path to the MCP server endpoint (optional)

### Server Status

Get information about an MCP server and your API key usage:

```
GET https://gumroad.com/api/mcp/{server_id}/status
```

Response:
```json
{
  "server": {
    "server_id": "abc123",
    "name": "My MCP Server",
    "endpoint_url": "https://my-server.com/api",
    "health_status": "healthy",
    "is_active": true,
    "last_health_check": "2025-06-20T12:00:00Z",
    "supported_tools": ["file_operations", "web_search"],
    "pricing_model": "per_request",
    "uptime_percentage": 99.5,
    "average_response_time": 150.2
  },
  "rate_limits": [
    {
      "window": "hourly",
      "window_seconds": 3600,
      "current": 45,
      "limit": 1000,
      "remaining": 955,
      "reset_at": "2025-06-20T13:00:00Z"
    }
  ],
  "api_key": {
    "id": 123,
    "permissions": ["read", "execute"],
    "expires_at": "2026-06-20T12:00:00Z"
  }
}
```

## Rate Limiting

The gateway implements multiple levels of rate limiting:

### Global Rate Limits (per IP)
- **Unauthenticated requests**: 20 requests per minute
- **Authenticated requests**: 100 requests per minute

### API Key Rate Limits
Configured per MCP server and API key:
- **Hourly limit**: Default 1,000 requests
- **Daily limit**: Default 10,000 requests  
- **Monthly limit**: Default 100,000 requests

### Rate Limit Headers

All responses include rate limit information:

```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 955
X-RateLimit-Reset: 1719748800
```

When rate limited:
```
HTTP 429 Too Many Requests
Retry-After: 3600
```

## Error Responses

### Authentication Errors

```json
HTTP 401 Unauthorized
{
  "error": "Invalid API key",
  "message": "API key is invalid or expired"
}
```

### Permission Errors

```json
HTTP 403 Forbidden
{
  "error": "Insufficient permissions",
  "message": "API key does not have required permissions"
}
```

### Rate Limit Errors

```json
HTTP 429 Too Many Requests
{
  "error": "Rate limit exceeded",
  "message": "Hourly rate limit exceeded",
  "retry_after": 3600
}
```

### Server Errors

```json
HTTP 503 Service Unavailable
{
  "error": "Server unavailable",
  "message": "MCP server is currently inactive"
}
```

```json
HTTP 504 Gateway Timeout
{
  "error": "Request timeout",
  "message": "Request to MCP server timed out"
}
```

## Usage Examples

### Basic Request

```bash
curl -H "Authorization: Bearer your_api_key" \
     https://gumroad.com/api/mcp/abc123/search \
     -d '{"query": "hello world"}'
```

### Check Server Status

```bash
curl -H "Authorization: Bearer your_api_key" \
     https://gumroad.com/api/mcp/abc123/status
```

### File Upload

```bash
curl -H "Authorization: Bearer your_api_key" \
     -F "file=@document.pdf" \
     https://gumroad.com/api/mcp/abc123/upload
```

## Best Practices

1. **Store API keys securely** - Never expose API keys in client-side code
2. **Handle rate limits gracefully** - Implement exponential backoff
3. **Monitor usage** - Check the status endpoint regularly
4. **Cache responses** - Reduce API calls where possible
5. **Handle errors** - Implement proper error handling for all error types

## Pricing Models

The gateway supports different pricing models:

### Fixed Price
- One-time payment for unlimited access
- No per-request charges

### Per-Request
- Pay per API call
- Includes free tier (configurable)
- Tiered pricing available

### Hybrid
- Base monthly fee with included requests
- Per-request charges for overages

## Monitoring and Logging

All requests are logged with:
- Request method and path
- Response status and timing
- API key usage
- Error details
- Rate limit status

Server health is monitored continuously with automatic failover for unhealthy servers.

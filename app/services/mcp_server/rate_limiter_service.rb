# frozen_string_literal: true

class McpServer::RateLimiterService
  REDIS_KEY_PREFIX = "mcp_rate_limit"
  DEFAULT_WINDOW = 1.hour
  DEFAULT_LIMIT = 1000

  def initialize(api_key_record)
    @api_key_record = api_key_record
    @redis = Redis.current
  end

  def check_rate_limit(window: DEFAULT_WINDOW, custom_limit: nil)
    limit = custom_limit || get_rate_limit_for_window(window)
    key = build_redis_key(window)
    
    current_count = @redis.get(key).to_i
    
    if current_count >= limit
      {
        allowed: false,
        limit: limit,
        current: current_count,
        reset_at: get_window_reset_time(window),
        retry_after: calculate_retry_after(window)
      }
    else
      # Increment the counter
      @redis.multi do |multi|
        multi.incr(key)
        multi.expire(key, window.to_i)
      end
      
      {
        allowed: true,
        limit: limit,
        current: current_count + 1,
        reset_at: get_window_reset_time(window),
        retry_after: nil
      }
    end
  end

  def get_current_usage(window: DEFAULT_WINDOW)
    key = build_redis_key(window)
    current_count = @redis.get(key).to_i
    limit = get_rate_limit_for_window(window)
    
    {
      current: current_count,
      limit: limit,
      remaining: [limit - current_count, 0].max,
      reset_at: get_window_reset_time(window),
      window_seconds: window.to_i
    }
  end

  def reset_rate_limit(window: DEFAULT_WINDOW)
    key = build_redis_key(window)
    @redis.del(key)
  end

  def get_rate_limits_summary
    windows = [1.hour, 1.day, 1.month]
    
    windows.map do |window|
      usage = get_current_usage(window: window)
      {
        window: window_name(window),
        window_seconds: window.to_i,
        **usage
      }
    end
  end

  def self.check_global_rate_limit(ip_address, window: 1.minute, limit: 100)
    redis = Redis.current
    key = "#{REDIS_KEY_PREFIX}:global:#{ip_address}:#{window.to_i}"
    
    current_count = redis.get(key).to_i
    
    if current_count >= limit
      {
        allowed: false,
        limit: limit,
        current: current_count,
        reset_at: Time.current + window,
        retry_after: window.to_i
      }
    else
      redis.multi do |multi|
        multi.incr(key)
        multi.expire(key, window.to_i)
      end
      
      {
        allowed: true,
        limit: limit,
        current: current_count + 1,
        reset_at: Time.current + window,
        retry_after: nil
      }
    end
  end

  def self.get_server_rate_limits(mcp_server_config)
    {
      requests_per_hour: mcp_server_config.rate_limits.dig("requests_per_hour") || 1000,
      requests_per_day: mcp_server_config.rate_limits.dig("requests_per_day") || 10000,
      requests_per_month: mcp_server_config.rate_limits.dig("requests_per_month") || 100000,
      burst_limit: mcp_server_config.rate_limits.dig("burst_limit") || 50,
      concurrent_requests: mcp_server_config.rate_limits.dig("concurrent_requests") || 10
    }
  end

  private

  attr_reader :api_key_record, :redis

  def build_redis_key(window)
    "#{REDIS_KEY_PREFIX}:#{api_key_record.id}:#{window.to_i}"
  end

  def get_rate_limit_for_window(window)
    rate_limits = api_key_record.rate_limits
    
    case window
    when 1.hour
      rate_limits["requests_per_hour"] || DEFAULT_LIMIT
    when 1.day
      rate_limits["requests_per_day"] || (DEFAULT_LIMIT * 24)
    when 1.month
      rate_limits["requests_per_month"] || (DEFAULT_LIMIT * 24 * 30)
    else
      # For custom windows, calculate based on hourly rate
      hourly_rate = rate_limits["requests_per_hour"] || DEFAULT_LIMIT
      (hourly_rate * (window.to_f / 1.hour)).round
    end
  end

  def get_window_reset_time(window)
    case window
    when 1.hour
      Time.current.beginning_of_hour + 1.hour
    when 1.day
      Time.current.beginning_of_day + 1.day
    when 1.month
      Time.current.beginning_of_month + 1.month
    else
      Time.current + window
    end
  end

  def calculate_retry_after(window)
    (get_window_reset_time(window) - Time.current).to_i
  end

  def window_name(window)
    case window
    when 1.hour then "hourly"
    when 1.day then "daily"
    when 1.month then "monthly"
    else "#{window.to_i}s"
    end
  end
end

# frozen_string_literal: true

class McpServer::GitHubImportService
  def initialize(github_url:)
    @github_url = github_url
  end

  def process
    return { success: false, errors: ["GitHub URL is required"] } if github_url.blank?

    begin
      repo_info = extract_repo_info
      return { success: false, errors: ["Invalid GitHub URL"] } unless repo_info

      mcp_server_data = fetch_mcp_server_data(repo_info)

      { success: true, mcp_server_data: mcp_server_data }
    rescue StandardError => e
      Rails.logger.error "GitHub import failed: #{e.message}"
      { success: false, errors: ["Failed to import from GitHub: #{e.message}"] }
    end
  end

  private

  attr_reader :github_url

  def extract_repo_info
    # Parse GitHub URL to extract owner and repo name
    # Supports formats like:
    # - https://github.com/owner/repo
    # - https://github.com/owner/repo.git
    # - **************:owner/repo.git

    url = github_url.strip

    # Handle SSH format first
    if url.match(/git@github\.com:(.+)\/(.+)\.git/)
      return {
        owner: $1,
        repo: $2
      }
    end

    # Handle HTTPS format
    begin
      uri = URI.parse(url)

      if uri.host == "github.com"
        path_parts = uri.path.gsub(/^\/|\.git$/, "").split("/")
        return nil unless path_parts.length >= 2

        {
          owner: path_parts[0],
          repo: path_parts[1]
        }
      else
        nil
      end
    rescue URI::InvalidURIError
      nil
    end
  end

  def fetch_mcp_server_data(repo_info)
    # Fetch repository information from GitHub API
    repo_data = fetch_github_repo(repo_info)
    package_json = fetch_package_json(repo_info)
    readme_content = fetch_readme(repo_info)

    # Extract MCP server metadata
    {
      name: extract_name(repo_data, package_json),
      description: extract_description(repo_data, package_json, readme_content),
      endpoint_url: extract_endpoint_url(repo_data, package_json),
      supported_tools: extract_supported_tools(package_json, readme_content),
      api_documentation: extract_api_documentation(readme_content),
      server_metadata: {
        github_url: github_url,
        github_stars: repo_data&.dig("stargazers_count") || 0,
        github_language: repo_data&.dig("language"),
        last_updated: repo_data&.dig("updated_at")
      }
    }
  end

  def fetch_github_repo(repo_info)
    # Use GitHub API to fetch repository information
    # Note: In production, you might want to use a GitHub token for higher rate limits
    uri = URI("https://api.github.com/repos/#{repo_info[:owner]}/#{repo_info[:repo]}")

    response = Net::HTTP.get_response(uri)
    return nil unless response.code == "200"

    JSON.parse(response.body)
  rescue StandardError => e
    Rails.logger.warn "Failed to fetch GitHub repo data: #{e.message}"
    nil
  end

  def fetch_package_json(repo_info)
    # Fetch package.json if it exists (for Node.js MCP servers)
    uri = URI("https://api.github.com/repos/#{repo_info[:owner]}/#{repo_info[:repo]}/contents/package.json")

    response = Net::HTTP.get_response(uri)
    return nil unless response.code == "200"

    content_data = JSON.parse(response.body)
    return nil unless content_data["content"]

    # Decode base64 content
    package_content = Base64.decode64(content_data["content"])
    JSON.parse(package_content)
  rescue StandardError => e
    Rails.logger.warn "Failed to fetch package.json: #{e.message}"
    nil
  end

  def fetch_readme(repo_info)
    # Try to fetch README.md or README.txt
    %w[README.md README.txt README].each do |filename|
      uri = URI("https://api.github.com/repos/#{repo_info[:owner]}/#{repo_info[:repo]}/contents/#{filename}")

      response = Net::HTTP.get_response(uri)
      next unless response.code == "200"

      content_data = JSON.parse(response.body)
      next unless content_data["content"]

      return Base64.decode64(content_data["content"])
    end

    nil
  rescue StandardError => e
    Rails.logger.warn "Failed to fetch README: #{e.message}"
    nil
  end

  def extract_name(repo_data, package_json)
    # Priority: package.json name > repo name
    package_json&.dig("name") || repo_data&.dig("name") || "Imported MCP Server"
  end

  def extract_description(repo_data, package_json, readme_content)
    # Priority: package.json description > repo description > first line of README
    description = package_json&.dig("description") || repo_data&.dig("description")

    if description.blank? && readme_content.present?
      # Extract first meaningful line from README
      lines = readme_content.split("\n").map(&:strip).reject(&:blank?)
      description = lines.find { |line| !line.start_with?("#") && line.length > 10 }
    end

    description || "An MCP server imported from GitHub"
  end

  def extract_endpoint_url(repo_data, package_json)
    # Try to extract endpoint URL from package.json scripts or repo homepage
    homepage = repo_data&.dig("homepage")
    return homepage if homepage.present? && homepage.match?(/^https?:\/\//)

    # Default placeholder - user will need to update this
    "https://api.example.com/mcp"
  end

  def extract_supported_tools(package_json, readme_content)
    tools = []

    # Look for common MCP tool patterns in package.json keywords
    keywords = package_json&.dig("keywords") || []
    mcp_keywords = keywords.select { |k| k.match?(/tool|search|analyze|generate|translate|api/) }
    tools.concat(mcp_keywords)

    # Look for tool mentions in README
    if readme_content.present?
      # Simple pattern matching for common tool types
      tool_patterns = {
        "search" => /search|find|query/i,
        "analyze" => /analy[sz]e|process|parse/i,
        "generate" => /generate|create|build/i,
        "translate" => /translate|convert|transform/i,
        "api" => /api|endpoint|service/i
      }

      tool_patterns.each do |tool, pattern|
        tools << tool if readme_content.match?(pattern)
      end
    end

    tools.uniq.first(10) # Limit to 10 tools
  end

  def extract_api_documentation(readme_content)
    return "" if readme_content.blank?

    # Try to extract API documentation section from README
    lines = readme_content.split("\n")
    api_section_start = lines.find_index { |line| line.match?(/^#+\s*(API|Usage|Documentation|Getting Started)/i) }

    if api_section_start
      # Extract from API section to next major heading or end of file
      api_lines = []
      (api_section_start + 1...lines.length).each do |i|
        line = lines[i]
        break if line.match?(/^#+\s/) && !line.match?(/^#{lines[api_section_start][/^#+/]}#/)
        api_lines << line
      end

      api_lines.join("\n").strip
    else
      # Return first 500 characters of README as fallback
      readme_content[0, 500]
    end
  end
end

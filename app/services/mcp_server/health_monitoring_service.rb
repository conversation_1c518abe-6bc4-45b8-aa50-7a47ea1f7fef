# frozen_string_literal: true

class McpServer::HealthMonitoringService
  HEALTH_CHECK_INTERVAL = 5.minutes
  ALERT_THRESHOLD = 3 # consecutive failures before alerting

  def self.schedule_all_health_checks
    new.schedule_all_health_checks
  end

  def self.process_health_alerts
    new.process_health_alerts
  end

  def initialize
    @logger = Rails.logger
  end

  def schedule_all_health_checks
    @logger.info "Scheduling health checks for all active MCP servers"
    
    McpServerConfig.active.find_each do |config|
      if config.needs_health_check?
        config.schedule_health_check!
        @logger.debug "Scheduled health check for MCP server: #{config.endpoint_url}"
      end
    end
  end

  def schedule_health_check(mcp_server_config_id)
    config = McpServerConfig.find(mcp_server_config_id)
    config.schedule_health_check!
  end

  def process_health_alerts
    @logger.info "Processing health alerts for MCP servers"
    
    McpServerConfig.unhealthy.find_each do |config|
      if config.should_alert_for_downtime?
        send_downtime_alert(config)
      end
    end
  end

  def get_health_dashboard_data
    {
      total_servers: McpServerConfig.active.count,
      healthy_servers: McpServerConfig.healthy.count,
      unhealthy_servers: McpServerConfig.unhealthy.count,
      unknown_status_servers: McpServerConfig.unknown_health.count,
      recent_checks: recent_health_checks_summary,
      server_health_overview: server_health_overview,
      uptime_trends: calculate_uptime_trends
    }
  end

  def get_server_health_details(mcp_server_config_id)
    config = McpServerConfig.find(mcp_server_config_id)
    
    {
      server: {
        id: config.id,
        endpoint_url: config.endpoint_url,
        health_status: config.health_status,
        last_health_check_at: config.last_health_check_at,
        is_active: config.is_active
      },
      health_summary: config.recent_health_summary,
      uptime_percentage: config.uptime_percentage,
      average_response_time: config.average_response_time,
      consecutive_failures: config.consecutive_failures,
      recent_checks: config.mcp_server_health_checks
                           .order(checked_at: :desc)
                           .limit(20)
                           .as_json,
      daily_uptime: calculate_daily_uptime(config),
      response_time_trend: calculate_response_time_trend(config)
    }
  end

  private

  def send_downtime_alert(config)
    # In a real implementation, you'd send emails, Slack notifications, etc.
    @logger.warn "ALERT: MCP Server #{config.endpoint_url} has #{config.consecutive_failures} consecutive failures"
    
    # You could integrate with notification services here:
    # - Send email to server owner
    # - Post to Slack channel
    # - Create incident in monitoring system
    # - Update status page
    
    # For now, just log the alert
    alert_data = {
      server_id: config.id,
      endpoint_url: config.endpoint_url,
      consecutive_failures: config.consecutive_failures,
      last_successful_check: config.last_successful_check&.checked_at,
      alert_sent_at: Time.current
    }
    
    @logger.error "MCP Server Downtime Alert: #{alert_data.to_json}"
  end

  def recent_health_checks_summary
    recent_checks = McpServerHealthCheck.recent.order(checked_at: :desc).limit(100)
    
    {
      total_checks: recent_checks.count,
      successful_checks: recent_checks.successful.count,
      failed_checks: recent_checks.failed.count,
      average_response_time: recent_checks.successful.average(:response_time_ms)&.round(2) || 0.0
    }
  end

  def server_health_overview
    McpServerConfig.active.includes(:mcp_server_health_checks).map do |config|
      {
        id: config.id,
        endpoint_url: config.endpoint_url,
        health_status: config.health_status,
        last_check_at: config.last_health_check_at,
        uptime_percentage: config.uptime_percentage(days: 1), # Last 24 hours
        average_response_time: config.average_response_time(days: 1),
        consecutive_failures: config.consecutive_failures
      }
    end
  end

  def calculate_uptime_trends
    # Calculate uptime trends for the last 7 days
    (0..6).map do |days_ago|
      date = days_ago.days.ago.to_date
      start_time = date.beginning_of_day
      end_time = date.end_of_day
      
      checks = McpServerHealthCheck.where(checked_at: start_time..end_time)
      uptime = checks.empty? ? 100.0 : McpServerHealthCheck.uptime_percentage(start_date: start_time, end_date: end_time)
      
      {
        date: date.iso8601,
        uptime_percentage: uptime,
        total_checks: checks.count,
        successful_checks: checks.successful.count
      }
    end.reverse
  end

  def calculate_daily_uptime(config)
    # Calculate daily uptime for the last 7 days
    (0..6).map do |days_ago|
      date = days_ago.days.ago.to_date
      start_time = date.beginning_of_day
      end_time = date.end_of_day
      
      checks = config.mcp_server_health_checks.where(checked_at: start_time..end_time)
      uptime = checks.empty? ? 100.0 : checks.uptime_percentage(start_date: start_time, end_date: end_time)
      
      {
        date: date.iso8601,
        uptime_percentage: uptime,
        total_checks: checks.count
      }
    end.reverse
  end

  def calculate_response_time_trend(config)
    # Calculate average response time for the last 24 hours, grouped by hour
    (0..23).map do |hours_ago|
      start_time = hours_ago.hours.ago.beginning_of_hour
      end_time = start_time.end_of_hour
      
      avg_response_time = config.mcp_server_health_checks
                               .successful
                               .where(checked_at: start_time..end_time)
                               .average(:response_time_ms)&.round(2) || 0.0
      
      {
        hour: start_time.iso8601,
        average_response_time: avg_response_time
      }
    end.reverse
  end
end

# frozen_string_literal: true

class McpServer::ApiKeyService
  class ApiKeyError < StandardError; end
  class RateLimitExceededError < ApiKeyError; end
  class InvalidApiKeyError < ApiKeyError; end
  class InsufficientPermissionsError < ApiKeyError; end

  def initialize(api_key = nil)
    @api_key = api_key
    @api_key_record = nil
  end

  def self.create_for_purchase(purchase:, link:, name: nil, permissions: [], rate_limits: {})
    new.create_for_purchase(
      purchase: purchase,
      link: link,
      name: name,
      permissions: permissions,
      rate_limits: rate_limits
    )
  end

  def self.authenticate_and_authorize(api_key:, link:, permission: nil)
    new(api_key).authenticate_and_authorize(link: link, permission: permission)
  end

  def create_for_purchase(purchase:, link:, name: nil, permissions: [], rate_limits: {})
    # Check if API key already exists for this purchase/link combination
    existing_key = McpServerApiKey.find_by(purchase: purchase, link: link)
    if existing_key
      return {
        success: false,
        error: "API key already exists for this purchase",
        api_key_id: existing_key.id
      }
    end

    # Validate that the purchase is for the correct link
    unless purchase.link == link
      return {
        success: false,
        error: "Purchase does not match the specified MCP server"
      }
    end

    # Validate that the purchase is successful
    unless purchase.successful?
      return {
        success: false,
        error: "Purchase must be successful to generate API key"
      }
    end

    begin
      api_key_record = McpServerApiKey.create!(
        purchase: purchase,
        link: link,
        name: name || "API Key for #{link.name}",
        permissions: permissions,
        rate_limits: default_rate_limits.merge(rate_limits)
      )

      {
        success: true,
        api_key: api_key_record.api_key,
        api_key_id: api_key_record.id,
        masked_key: api_key_record.masked_key
      }
    rescue ActiveRecord::RecordInvalid => e
      {
        success: false,
        error: e.record.errors.full_messages.join(", ")
      }
    end
  end

  def authenticate_and_authorize(link:, permission: nil)
    # Authenticate the API key
    @api_key_record = McpServerApiKey.authenticate(@api_key)
    
    unless @api_key_record
      raise InvalidApiKeyError, "Invalid or expired API key"
    end

    # Check if the API key is for the correct server
    unless @api_key_record.link == link
      raise InvalidApiKeyError, "API key is not valid for this MCP server"
    end

    # Check rate limits
    if @api_key_record.rate_limit_exceeded?
      raise RateLimitExceededError, "Rate limit exceeded"
    end

    # Check permissions if specified
    if permission && !@api_key_record.has_permission?(permission)
      raise InsufficientPermissionsError, "Insufficient permissions for this operation"
    end

    @api_key_record
  end

  def record_api_usage(response_time_ms: 0, success: true, metadata: {})
    return unless @api_key_record

    McpServerUsage.record_api_call(
      link: @api_key_record.link,
      purchase: @api_key_record.purchase,
      api_key_hash: @api_key_record.key_hash,
      response_time_ms: response_time_ms,
      success: success,
      metadata: metadata
    )
  end

  def get_usage_stats(days: 30)
    return {} unless @api_key_record
    @api_key_record.usage_stats(days: days)
  end

  def revoke_api_key(api_key_id:, user:)
    api_key_record = McpServerApiKey.find(api_key_id)
    
    # Check if user has permission to revoke this key
    unless can_manage_api_key?(api_key_record, user)
      return {
        success: false,
        error: "Unauthorized to revoke this API key"
      }
    end

    api_key_record.revoke!
    
    {
      success: true,
      message: "API key revoked successfully"
    }
  end

  def suspend_api_key(api_key_id:, user:)
    api_key_record = McpServerApiKey.find(api_key_id)
    
    unless can_manage_api_key?(api_key_record, user)
      return {
        success: false,
        error: "Unauthorized to suspend this API key"
      }
    end

    api_key_record.suspend!
    
    {
      success: true,
      message: "API key suspended successfully"
    }
  end

  def activate_api_key(api_key_id:, user:)
    api_key_record = McpServerApiKey.find(api_key_id)
    
    unless can_manage_api_key?(api_key_record, user)
      return {
        success: false,
        error: "Unauthorized to activate this API key"
      }
    end

    api_key_record.activate!
    
    {
      success: true,
      message: "API key activated successfully"
    }
  end

  def list_api_keys_for_user(user:, link: nil)
    # Get all purchases by the user for MCP servers
    purchases = user.purchases.successful.joins(:link)
                   .where(links: { native_type: Link::NATIVE_TYPE_MCP_SERVER })
    
    purchases = purchases.where(link: link) if link
    
    api_keys = McpServerApiKey.joins(:purchase)
                             .where(purchase: purchases)
                             .includes(:link, :purchase)
    
    api_keys.map(&:as_json)
  end

  private

  def default_rate_limits
    {
      "requests_per_hour" => 1000,
      "requests_per_day" => 10000,
      "requests_per_month" => 100000
    }
  end

  def can_manage_api_key?(api_key_record, user)
    # User can manage API keys for their own purchases or if they're the server owner
    api_key_record.purchase.user == user || api_key_record.link.user == user
  end
end

# frozen_string_literal: true

class McpServer::UpdateService
  def initialize(mcp_server:, params:)
    @mcp_server = mcp_server
    @params = params
  end

  def process
    ActiveRecord::Base.transaction do
      update_link
      update_mcp_server_config

      { success: true, mcp_server_id: @mcp_server.unique_permalink }
    rescue ActiveRecord::RecordInvalid => e
      { success: false, errors: e.record.errors.full_messages }
    rescue StandardError => e
      { success: false, errors: [e.message] }
    end
  end

  private

  attr_reader :mcp_server, :params

  def update_link
    @mcp_server.update!(link_params)
  end

  def update_mcp_server_config
    config = @mcp_server.mcp_server_config
    config.update!(config_params)
  end

  def link_params
    {
      name: params[:name],
      description: params[:description],
      price_cents: params[:price_cents]
    }.compact
  end

  def config_params
    {
      endpoint_url: params[:endpoint_url],
      api_documentation: params[:api_documentation],
      github_url: params[:github_url],
      supported_tools: params[:supported_tools],
      server_metadata: params[:server_metadata]
    }.compact
  end
end

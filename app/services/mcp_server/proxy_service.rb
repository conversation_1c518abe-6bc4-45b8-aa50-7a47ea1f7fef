# frozen_string_literal: true

class McpServer::ProxyService
  include HTTParty
  
  class ProxyError < StandardError; end
  class ServerUnavailableError < ProxyError; end
  class TimeoutError < ProxyError; end
  class RateLimitError < ProxyError; end

  def initialize(mcp_server:, api_key_record:, request:)
    @mcp_server = mcp_server
    @api_key_record = api_key_record
    @request = request
    @config = mcp_server.mcp_server_config
  end

  def proxy_request
    validate_server_availability!
    check_rate_limits!
    
    start_time = Time.current
    
    begin
      response = execute_proxied_request
      
      record_usage(
        response_time_ms: calculate_response_time(start_time),
        success: response.success?,
        response: response
      )
      
      {
        success: true,
        status: response.code,
        headers: extract_response_headers(response),
        body: response.body,
        response_time_ms: calculate_response_time(start_time)
      }
      
    rescue Net::TimeoutError, Net::OpenTimeout, Net::ReadTimeout => e
      record_usage(
        response_time_ms: calculate_response_time(start_time),
        success: false,
        error: e
      )
      
      raise TimeoutError, "Request to MCP server timed out"
      
    rescue StandardError => e
      record_usage(
        response_time_ms: calculate_response_time(start_time),
        success: false,
        error: e
      )
      
      Rails.logger.error "MCP Proxy error: #{e.message}"
      raise ProxyError, "Failed to proxy request: #{e.message}"
    end
  end

  def self.get_server_status(mcp_server)
    config = mcp_server.mcp_server_config
    
    {
      server_id: mcp_server.external_id,
      name: mcp_server.name,
      endpoint_url: config.endpoint_url,
      health_status: config.health_status,
      is_active: config.is_active,
      last_health_check: config.last_health_check_at,
      supported_tools: config.supported_tools,
      pricing_model: config.pricing_model,
      uptime_percentage: config.uptime_percentage(days: 7),
      average_response_time: config.average_response_time(days: 7)
    }
  end

  private

  attr_reader :mcp_server, :api_key_record, :request, :config

  def validate_server_availability!
    unless config.is_active?
      raise ServerUnavailableError, "MCP server is currently inactive"
    end

    unless config.healthy?
      raise ServerUnavailableError, "MCP server is currently unhealthy"
    end
  end

  def check_rate_limits!
    if api_key_record.rate_limit_exceeded?
      raise RateLimitError, "API rate limit exceeded"
    end
  end

  def execute_proxied_request
    target_url = build_target_url
    
    options = {
      timeout: 30,
      open_timeout: 10,
      headers: build_proxy_headers,
      follow_redirects: false # We'll handle redirects manually if needed
    }
    
    case request.method.upcase
    when 'GET'
      HTTParty.get(target_url, options)
    when 'POST'
      options[:body] = request.body.read if request.body
      options[:headers]['Content-Type'] = request.content_type if request.content_type
      HTTParty.post(target_url, options)
    when 'PUT'
      options[:body] = request.body.read if request.body
      options[:headers]['Content-Type'] = request.content_type if request.content_type
      HTTParty.put(target_url, options)
    when 'PATCH'
      options[:body] = request.body.read if request.body
      options[:headers]['Content-Type'] = request.content_type if request.content_type
      HTTParty.patch(target_url, options)
    when 'DELETE'
      HTTParty.delete(target_url, options)
    else
      HTTParty.get(target_url, options)
    end
  end

  def build_target_url
    # Extract the path after the gateway prefix
    gateway_prefix = "/api/mcp/#{mcp_server.external_id}"
    target_path = request.path.sub(gateway_prefix, '')
    target_path = '/' if target_path.empty?
    
    # Preserve query parameters
    query_string = request.query_string.present? ? "?#{request.query_string}" : ''
    
    "#{config.endpoint_url.chomp('/')}#{target_path}#{query_string}"
  end

  def build_proxy_headers
    headers = {}
    
    # Copy safe headers from the original request
    safe_headers = %w[
      accept accept-encoding accept-language cache-control
      content-type user-agent x-requested-with
    ]
    
    request.headers.each do |key, value|
      normalized_key = key.downcase.gsub('_', '-')
      if safe_headers.include?(normalized_key)
        headers[key] = value
      end
    end
    
    # Add custom headers for the MCP server
    headers.merge!(
      'User-Agent' => 'Gumroad-MCP-Gateway/1.0',
      'X-Forwarded-For' => request.remote_ip,
      'X-API-Key-ID' => api_key_record.id.to_s,
      'X-Purchase-ID' => api_key_record.purchase_id.to_s,
      'X-Gumroad-Request-ID' => SecureRandom.uuid
    )
    
    headers
  end

  def extract_response_headers(response)
    # Extract safe headers to return to the client
    safe_headers = %w[
      content-type content-length cache-control expires
      last-modified etag x-ratelimit-limit x-ratelimit-remaining
    ]
    
    headers = {}
    response.headers.each do |key, value|
      normalized_key = key.downcase
      if safe_headers.include?(normalized_key)
        headers[key] = value
      end
    end
    
    # Add custom headers
    headers['X-Served-By'] = 'Gumroad-MCP-Gateway'
    headers['X-MCP-Server-ID'] = mcp_server.external_id
    headers['X-Response-Time'] = "#{calculate_response_time(Time.current)}ms"
    
    headers
  end

  def calculate_response_time(start_time)
    ((Time.current - start_time) * 1000).round(2)
  end

  def record_usage(response_time_ms:, success:, response: nil, error: nil)
    metadata = {
      method: request.method,
      path: request.path,
      user_agent: request.user_agent,
      remote_ip: request.remote_ip
    }
    
    if response
      metadata.merge!(
        status_code: response.code,
        response_size: response.body&.bytesize || 0,
        content_type: response.headers['content-type']
      )
    end
    
    if error
      metadata.merge!(
        error_message: error.message,
        error_class: error.class.name
      )
    end
    
    McpServerUsage.record_api_call(
      link: mcp_server,
      purchase: api_key_record.purchase,
      api_key_hash: api_key_record.key_hash,
      response_time_ms: response_time_ms,
      success: success,
      metadata: metadata
    )
  end
end

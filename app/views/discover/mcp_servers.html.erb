<% content_for :title, "MCP Server Marketplace | Gumroad" %>
<% content_for :meta_description, "Discover and purchase Model Context Protocol servers to extend your AI applications. Browse hundreds of MCP servers from developers worldwide." %>

<%= render("layouts/custom_styles/style") %>
<%= render("shared/js_needed_notice") %>

<%= load_pack("discover") %>

<%= react_component "McpServersDiscoverPage", props: @react_discover_props, prerender: false %>

<style>
  .mcp-servers-discover-page {
    min-height: 100vh;
    background-color: #f8fafc;
  }

  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0 3rem;
    text-align: center;
  }

  .page-header__content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  .page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
  }

  .page-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
  }

  .discover-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 2rem;
  }

  .discover-sidebar {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    height: fit-content;
    position: sticky;
    top: 2rem;
  }

  .search-form {
    margin-bottom: 2rem;
  }

  .search-input-group {
    position: relative;
  }

  .search-input {
    width: 100%;
    padding: 0.75rem 3rem 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s;
  }

  .search-input:focus {
    outline: none;
    border-color: #667eea;
  }

  .search-button {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 0.5rem;
  }

  .filter-section {
    margin-bottom: 2rem;
  }

  .filter-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1e293b;
  }

  .sort-options {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .sort-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.2s;
  }

  .sort-option:hover {
    background-color: #f1f5f9;
  }

  .tag-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .tag-filter {
    background: #f1f5f9;
    border: 1px solid #e2e8f0;
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
  }

  .tag-filter:hover {
    background: #e2e8f0;
  }

  .tag-filter--active {
    background: #667eea;
    color: white;
    border-color: #667eea;
  }

  .tag-count {
    opacity: 0.7;
    margin-left: 0.25rem;
  }

  .discover-main {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .results-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .results-info {
    margin-bottom: 1rem;
  }

  .results-count {
    font-weight: 600;
    color: #1e293b;
  }

  .search-query {
    color: #64748b;
    margin-left: 0.5rem;
  }

  .active-filters {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .filter-label {
    font-weight: 500;
    color: #64748b;
  }

  .active-filter {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 16px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .remove-filter {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
  }

  .mcp-servers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #64748b;
  }

  .empty-state h3 {
    margin: 1rem 0;
    color: #1e293b;
  }

  @media (max-width: 768px) {
    .discover-content {
      grid-template-columns: 1fr;
      padding: 1rem;
    }

    .page-title {
      font-size: 2rem;
    }

    .mcp-servers-grid {
      grid-template-columns: 1fr;
    }
  }
</style>

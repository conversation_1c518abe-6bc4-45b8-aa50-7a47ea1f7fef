# frozen_string_literal: true

class McpServerApiKey < ApplicationRecord
  include JsonData
  include TimestampScopes

  belongs_to :purchase, class_name: "Purchase"
  belongs_to :link, class_name: "Link"
  has_many :mcp_server_usages, foreign_key: "api_key_hash", primary_key: "key_hash", dependent: :destroy

  validates :purchase_id, presence: true, uniqueness: { scope: :link_id }
  validates :link_id, presence: true
  validates :key_hash, presence: true, uniqueness: true
  validates :name, presence: true, length: { maximum: 100 }
  validates :status, inclusion: { in: %w[active suspended revoked] }

  attr_json_data_accessor :permissions, default: -> { [] }
  attr_json_data_accessor :rate_limits, default: -> { {} }
  attr_json_data_accessor :metadata, default: -> { {} }

  scope :active, -> { where(status: "active") }
  scope :suspended, -> { where(status: "suspended") }
  scope :revoked, -> { where(status: "revoked") }
  scope :for_purchase, ->(purchase) { where(purchase: purchase) }
  scope :for_server, ->(link) { where(link: link) }

  before_validation :generate_api_key, on: :create
  before_validation :set_defaults, on: :create
  before_save :update_key_hash

  def self.authenticate(api_key)
    return nil if api_key.blank?
    
    key_hash = hash_api_key(api_key)
    api_key_record = active.find_by(key_hash: key_hash)
    
    return nil unless api_key_record
    return nil if api_key_record.expired?
    
    # Update last used timestamp
    api_key_record.update_column(:last_used_at, Time.current)
    
    api_key_record
  end

  def self.generate_key
    # Generate a secure random API key
    "gumroad_mcp_#{SecureRandom.hex(32)}"
  end

  def self.hash_api_key(api_key)
    # Use SHA-256 to hash the API key for storage
    Digest::SHA256.hexdigest("#{api_key}#{Rails.application.secret_key_base}")
  end

  def active?
    status == "active"
  end

  def suspended?
    status == "suspended"
  end

  def revoked?
    status == "revoked"
  end

  def expired?
    expires_at.present? && expires_at < Time.current
  end

  def suspend!
    update!(status: "suspended")
  end

  def revoke!
    update!(status: "revoked")
  end

  def activate!
    update!(status: "active")
  end

  def usage_stats(days: 30)
    start_date = days.days.ago.to_date
    usages = mcp_server_usages.where(usage_date: start_date..Date.current)
    
    {
      total_requests: usages.sum(:total_requests),
      successful_requests: usages.sum(:successful_requests),
      failed_requests: usages.sum(:failed_requests),
      total_response_time: usages.sum(:total_response_time_ms),
      average_response_time: usages.average(:average_response_time_ms)&.round(2) || 0.0,
      success_rate: calculate_success_rate(usages),
      daily_usage: daily_usage_breakdown(usages)
    }
  end

  def rate_limit_exceeded?(window: 1.hour, limit: nil)
    return false unless rate_limits.present?
    
    # Use custom limit or default from rate_limits
    requests_limit = limit || rate_limits.dig("requests_per_hour") || 1000
    
    recent_usage = mcp_server_usages
                    .where(created_at: window.ago..Time.current)
                    .sum(:api_calls_count)
    
    recent_usage >= requests_limit
  end

  def has_permission?(permission)
    return true if permissions.empty? # No restrictions if permissions not set
    permissions.include?(permission.to_s)
  end

  def masked_key
    return nil unless api_key.present?
    "#{api_key[0..15]}#{'*' * 20}#{api_key[-8..-1]}"
  end

  def as_json(options = {})
    super(options.merge(except: [:api_key, :key_hash])).merge(
      masked_key: masked_key,
      active?: active?,
      suspended?: suspended?,
      revoked?: revoked?,
      expired?: expired?,
      usage_stats: usage_stats(days: 7)
    )
  end

  private

  def generate_api_key
    self.api_key = self.class.generate_key if api_key.blank?
  end

  def set_defaults
    self.status ||= "active"
    self.name ||= "API Key #{Time.current.strftime('%Y-%m-%d %H:%M')}"
    self.expires_at ||= 1.year.from_now
    self.rate_limits ||= { "requests_per_hour" => 1000, "requests_per_day" => 10000 }
    self.permissions ||= []
  end

  def update_key_hash
    if api_key_changed?
      self.key_hash = self.class.hash_api_key(api_key)
    end
  end

  def calculate_success_rate(usages)
    total_requests = usages.sum(:total_requests)
    return 0.0 if total_requests.zero?
    
    successful_requests = usages.sum(:successful_requests)
    (successful_requests.to_f / total_requests * 100).round(2)
  end

  def daily_usage_breakdown(usages)
    usages.group(:usage_date)
          .sum(:total_requests)
          .transform_keys { |date| date.iso8601 }
  end
end

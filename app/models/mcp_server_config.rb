# frozen_string_literal: true

class McpServerConfig < ApplicationRecord
  include JsonData
  include TimestampScopes

  belongs_to :link, class_name: "Link"
  has_many :mcp_server_usages, foreign_key: "link_id", primary_key: "link_id", dependent: :destroy
  has_many :mcp_server_health_checks, dependent: :destroy

  validates :endpoint_url, presence: true, format: { with: URI::DEFAULT_PARSER.make_regexp(%w[http https]) }
  validates :link_id, presence: true, uniqueness: true
  validates :health_status, inclusion: { in: %w[healthy unhealthy unknown] }
  validates :pricing_model, inclusion: { in: %w[fixed per_request hybrid] }
  validates :per_request_price_cents, numericality: { greater_than_or_equal_to: 0 }
  validates :free_tier_requests, numericality: { greater_than_or_equal_to: 0 }

  attr_json_data_accessor :supported_tools, default: -> { [] }
  attr_json_data_accessor :server_metadata, default: -> { {} }
  attr_json_data_accessor :pricing_tiers, default: -> { [] }

  scope :active, -> { where(is_active: true) }
  scope :inactive, -> { where(is_active: false) }
  scope :healthy, -> { where(health_status: "healthy") }
  scope :unhealthy, -> { where(health_status: "unhealthy") }
  scope :unknown_health, -> { where(health_status: "unknown") }

  before_validation :set_default_health_status, on: :create

  def healthy?
    health_status == "healthy"
  end

  def unhealthy?
    health_status == "unhealthy"
  end

  def unknown_health?
    health_status == "unknown"
  end

  def update_health_status!(status)
    update!(health_status: status, last_health_check_at: Time.current)
  end

  def needs_health_check?
    last_health_check_at.nil? || last_health_check_at < 5.minutes.ago
  end

  def schedule_health_check!
    McpServerHealthCheckJob.perform_async(id)
  end

  def uptime_percentage(days: 7)
    mcp_server_health_checks.uptime_percentage(start_date: days.days.ago)
  end

  def average_response_time(days: 7)
    mcp_server_health_checks.average_response_time(start_date: days.days.ago)
  end

  def recent_health_summary
    mcp_server_health_checks.health_summary
  end

  def last_successful_check
    mcp_server_health_checks.successful.order(checked_at: :desc).first
  end

  def last_failed_check
    mcp_server_health_checks.failed.order(checked_at: :desc).first
  end

  def consecutive_failures
    return 0 if healthy?

    # Count consecutive failures from the most recent check
    recent_checks = mcp_server_health_checks.order(checked_at: :desc).limit(10)
    consecutive_count = 0

    recent_checks.each do |check|
      if check.failure?
        consecutive_count += 1
      else
        break
      end
    end

    consecutive_count
  end

  def should_alert_for_downtime?
    consecutive_failures >= 3 # Alert after 3 consecutive failures
  end

  # Pricing model methods
  def fixed_pricing?
    pricing_model == "fixed"
  end

  def per_request_pricing?
    pricing_model == "per_request"
  end

  def hybrid_pricing?
    pricing_model == "hybrid"
  end

  def calculate_usage_cost(request_count)
    case pricing_model
    when "fixed"
      0 # Fixed pricing is handled by the base product price
    when "per_request"
      calculate_per_request_cost(request_count)
    when "hybrid"
      calculate_hybrid_cost(request_count)
    else
      0
    end
  end

  def get_pricing_tier_for_requests(request_count)
    return nil if pricing_tiers.empty?

    # Find the appropriate tier based on request count
    pricing_tiers.find { |tier| request_count <= tier["max_requests"] } ||
      pricing_tiers.last # Use the highest tier if request count exceeds all tiers
  end

  def monthly_cost_estimate(estimated_requests)
    base_cost = link.price_cents # Fixed cost from the product
    usage_cost = calculate_usage_cost(estimated_requests)

    {
      base_cost_cents: base_cost,
      usage_cost_cents: usage_cost,
      total_cost_cents: base_cost + usage_cost,
      estimated_requests: estimated_requests,
      pricing_model: pricing_model
    }
  end

  private

  def calculate_per_request_cost(request_count)
    return 0 if request_count <= free_tier_requests

    billable_requests = request_count - free_tier_requests

    if pricing_tiers.present?
      calculate_tiered_cost(billable_requests)
    else
      billable_requests * per_request_price_cents
    end
  end

  def calculate_hybrid_cost(request_count)
    # In hybrid model, there's a base monthly limit included in the fixed price
    return 0 if monthly_request_limit && request_count <= monthly_request_limit

    overage_requests = monthly_request_limit ? request_count - monthly_request_limit : request_count
    calculate_per_request_cost(overage_requests)
  end

  def calculate_tiered_cost(request_count)
    total_cost = 0
    remaining_requests = request_count

    pricing_tiers.each do |tier|
      tier_max = tier["max_requests"]
      tier_price = tier["price_cents_per_request"]

      if remaining_requests <= 0
        break
      elsif tier_max.nil? || remaining_requests <= tier_max
        # This tier covers all remaining requests
        total_cost += remaining_requests * tier_price
        break
      else
        # This tier is fully used
        total_cost += tier_max * tier_price
        remaining_requests -= tier_max
      end
    end

    total_cost
  end

  def as_json(options = {})
    super(options).merge(
      supported_tools: supported_tools,
      server_metadata: server_metadata
    )
  end

  private

  def set_default_health_status
    self.health_status ||= "unknown"
  end
end

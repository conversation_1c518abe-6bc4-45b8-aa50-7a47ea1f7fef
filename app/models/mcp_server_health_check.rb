# frozen_string_literal: true

class McpServerHealthCheck < ApplicationRecord
  include TimestampScopes

  belongs_to :mcp_server_config

  validates :checked_at, presence: true
  validates :response_time_ms, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :endpoint_url, presence: true

  scope :successful, -> { where(success: true) }
  scope :failed, -> { where(success: false) }
  scope :recent, -> { where(checked_at: 24.hours.ago..Time.current) }
  scope :for_date_range, ->(start_date, end_date) { where(checked_at: start_date..end_date) }

  def self.uptime_percentage(start_date: 7.days.ago, end_date: Time.current)
    checks = where(checked_at: start_date..end_date)
    return 100.0 if checks.empty?
    
    successful_count = checks.successful.count
    total_count = checks.count
    
    (successful_count.to_f / total_count * 100).round(2)
  end

  def self.average_response_time(start_date: 7.days.ago, end_date: Time.current)
    successful.where(checked_at: start_date..end_date).average(:response_time_ms)&.round(2) || 0.0
  end

  def self.recent_failures(limit: 10)
    failed.order(checked_at: :desc).limit(limit)
  end

  def self.health_summary(start_date: 7.days.ago, end_date: Time.current)
    checks = where(checked_at: start_date..end_date)
    
    {
      total_checks: checks.count,
      successful_checks: checks.successful.count,
      failed_checks: checks.failed.count,
      uptime_percentage: uptime_percentage(start_date: start_date, end_date: end_date),
      average_response_time: average_response_time(start_date: start_date, end_date: end_date),
      last_check_at: checks.maximum(:checked_at),
      last_successful_check_at: checks.successful.maximum(:checked_at),
      recent_failures: recent_failures(limit: 5).pluck(:checked_at, :error_message)
    }
  end

  def success?
    success
  end

  def failure?
    !success
  end

  def response_time_seconds
    response_time_ms / 1000.0
  end

  def as_json(options = {})
    super(options).merge(
      success?: success?,
      failure?: failure?,
      response_time_seconds: response_time_seconds
    )
  end
end

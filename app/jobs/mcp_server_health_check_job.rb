# frozen_string_literal: true

class McpServerHealthCheckJob
  include Sidekiq::Job

  sidekiq_options queue: :default, retry: 3

  def perform(mcp_server_config_id = nil)
    if mcp_server_config_id
      # Check specific server
      config = McpServerConfig.find(mcp_server_config_id)
      check_server_health(config)
    else
      # Check all active servers
      McpServerConfig.active.find_each do |config|
        check_server_health(config)
      end
    end
  end

  private

  def check_server_health(config)
    start_time = Time.current
    
    begin
      response = perform_health_check(config.endpoint_url)
      response_time = ((Time.current - start_time) * 1000).round(2)
      
      if response.success?
        config.update_health_status!("healthy")
        record_health_check_success(config, response_time, response.body)
      else
        config.update_health_status!("unhealthy")
        record_health_check_failure(config, response_time, "HTTP #{response.code}: #{response.message}")
      end
      
    rescue Net::TimeoutError, Net::OpenTimeout, Net::ReadTimeout => e
      response_time = ((Time.current - start_time) * 1000).round(2)
      config.update_health_status!("unhealthy")
      record_health_check_failure(config, response_time, "Timeout: #{e.message}")
      
    rescue StandardError => e
      response_time = ((Time.current - start_time) * 1000).round(2)
      config.update_health_status!("unhealthy")
      record_health_check_failure(config, response_time, "Error: #{e.message}")
      
      Rails.logger.error "MCP Server health check failed for #{config.endpoint_url}: #{e.message}"
      Bugsnag.notify(e) if defined?(Bugsnag)
    end
  end

  def perform_health_check(endpoint_url)
    uri = URI(endpoint_url)
    
    # Try to make a basic HTTP request to check if the server is responding
    # For MCP servers, we might want to check a specific health endpoint
    health_endpoint = build_health_endpoint(uri)
    
    Net::HTTP.start(uri.host, uri.port, use_ssl: uri.scheme == 'https', 
                    open_timeout: 10, read_timeout: 30) do |http|
      
      request = Net::HTTP::Get.new(health_endpoint)
      request['User-Agent'] = 'Gumroad-MCP-Health-Check/1.0'
      request['Accept'] = 'application/json'
      
      http.request(request)
    end
  end

  def build_health_endpoint(uri)
    # Try common health check endpoints
    health_paths = ['/health', '/status', '/ping', '/.well-known/health']
    
    # For now, just use the base path or try /health
    if uri.path.present? && uri.path != '/'
      uri.path
    else
      '/health'
    end
  end

  def record_health_check_success(config, response_time, response_body)
    create_health_check_record(config, true, response_time, response_body, nil)
  end

  def record_health_check_failure(config, response_time, error_message)
    create_health_check_record(config, false, response_time, nil, error_message)
  end

  def create_health_check_record(config, success, response_time, response_body, error_message)
    McpServerHealthCheck.create!(
      mcp_server_config: config,
      checked_at: Time.current,
      success: success,
      response_time_ms: response_time,
      response_body: response_body&.truncate(1000), # Limit response body size
      error_message: error_message&.truncate(500),
      endpoint_url: config.endpoint_url
    )
  end
end

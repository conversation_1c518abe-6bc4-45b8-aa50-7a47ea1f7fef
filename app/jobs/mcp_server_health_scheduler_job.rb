# frozen_string_literal: true

class McpServerHealthSchedulerJob
  include Sidekiq::Job

  sidekiq_options queue: :scheduler, retry: 1

  def perform
    Rails.logger.info "Running MCP Server health check scheduler"
    
    # Schedule health checks for all servers that need them
    McpServer::HealthMonitoringService.schedule_all_health_checks
    
    # Process any health alerts
    McpServer::HealthMonitoringService.process_health_alerts
    
    # Clean up old health check records (keep last 30 days)
    cleanup_old_health_checks
    
    Rails.logger.info "MCP Server health check scheduler completed"
  end

  private

  def cleanup_old_health_checks
    cutoff_date = 30.days.ago
    deleted_count = McpServerHealthCheck.where("checked_at < ?", cutoff_date).delete_all
    
    if deleted_count > 0
      Rails.logger.info "Cleaned up #{deleted_count} old MCP server health check records"
    end
  end
end

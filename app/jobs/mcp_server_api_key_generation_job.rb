# frozen_string_literal: true

class McpServerApiKeyGenerationJob
  include Sidekiq::Job

  sidekiq_options queue: :default, retry: 3

  def perform(purchase_id)
    purchase = Purchase.find(purchase_id)
    
    # Only generate API keys for MCP server purchases
    return unless purchase.link.native_type == Link::NATIVE_TYPE_MCP_SERVER
    
    # Only generate for successful purchases
    return unless purchase.successful?
    
    # Check if API key already exists
    existing_key = McpServerApiKey.find_by(purchase: purchase, link: purchase.link)
    return if existing_key
    
    begin
      service = McpServer::ApiKeyService.new
      result = service.create_for_purchase(
        purchase: purchase,
        link: purchase.link,
        name: "Auto-generated API Key for #{purchase.link.name}",
        permissions: default_permissions_for_purchase(purchase),
        rate_limits: default_rate_limits_for_purchase(purchase)
      )
      
      if result[:success]
        Rails.logger.info "Generated API key for purchase #{purchase_id}"
        
        # Send email notification to the purchaser with their API key
        send_api_key_notification(purchase, result[:api_key])
      else
        Rails.logger.error "Failed to generate API key for purchase #{purchase_id}: #{result[:error]}"
        Bugsnag.notify(StandardError.new("API key generation failed"), {
          purchase_id: purchase_id,
          error: result[:error]
        }) if defined?(Bugsnag)
      end
      
    rescue StandardError => e
      Rails.logger.error "Error generating API key for purchase #{purchase_id}: #{e.message}"
      Bugsnag.notify(e, { purchase_id: purchase_id }) if defined?(Bugsnag)
      raise e
    end
  end

  private

  def default_permissions_for_purchase(purchase)
    # Basic permissions for all purchasers
    permissions = ["read", "execute"]
    
    # Add additional permissions based on purchase type or amount
    if purchase.price_cents >= 5000 # $50 or more
      permissions << "admin"
    end
    
    permissions
  end

  def default_rate_limits_for_purchase(purchase)
    # Base rate limits
    rate_limits = {
      "requests_per_hour" => 1000,
      "requests_per_day" => 10000,
      "requests_per_month" => 100000
    }
    
    # Increase limits for higher-tier purchases
    if purchase.price_cents >= 10000 # $100 or more
      rate_limits["requests_per_hour"] = 5000
      rate_limits["requests_per_day"] = 50000
      rate_limits["requests_per_month"] = 500000
    elsif purchase.price_cents >= 5000 # $50 or more
      rate_limits["requests_per_hour"] = 2000
      rate_limits["requests_per_day"] = 20000
      rate_limits["requests_per_month"] = 200000
    end
    
    rate_limits
  end

  def send_api_key_notification(purchase, api_key)
    # In a real implementation, you'd send an email with the API key
    # For now, just log it (in production, you'd want to encrypt/secure this)
    
    Rails.logger.info "API Key generated for purchase #{purchase.id}"
    Rails.logger.info "User: #{purchase.user.email}"
    Rails.logger.info "MCP Server: #{purchase.link.name}"
    Rails.logger.info "API Key: #{api_key[0..15]}..." # Only log first part for security
    
    # TODO: Implement email notification
    # McpServerApiKeyMailer.api_key_generated(purchase, api_key).deliver_now
  end
end

# frozen_string_literal: true

class Api::Internal::McpServersController < Api::Internal::BaseController
  before_action :authenticate_user!
  after_action :verify_authorized
  before_action :set_mcp_server, only: %i[show edit update destroy]
  before_action :authorize_user

  def index
    authorize Link, :index?

    render json: McpServersPresenter.new(
      seller: current_seller,
      page: params[:page] || 1,
      query: params[:query]
    ).index_props
  end

  def show
    render json: McpServerPresenter.new(seller: current_seller, mcp_server: @mcp_server).show_props
  end

  def new
    authorize Link, :create?

    render json: McpServerPresenter.new(seller: current_seller).new_props
  end

  def create
    authorize Link, :create?

    service = McpServer::CreateService.new(seller: current_seller, params: mcp_server_params)
    response = service.process

    render json: response, status: response[:success] ? :created : :unprocessable_entity
  end

  def edit
    render json: McpServerPresenter.new(seller: current_seller, mcp_server: @mcp_server).edit_props
  end

  def update
    service = McpServer::UpdateService.new(mcp_server: @mcp_server, params: mcp_server_params)
    response = service.process

    render json: response, status: response[:success] ? :ok : :unprocessable_entity
  end

  def destroy
    @mcp_server.link.delete!

    render json: { success: true }
  end

  def import_from_github
    authorize Link, :create?

    service = McpServer::GitHubImportService.new(github_url: params[:github_url])
    response = service.process

    render json: response, status: response[:success] ? :ok : :unprocessable_entity
  end

  private

  def set_mcp_server
    @mcp_server = current_seller.links
                                .joins(:mcp_server_config)
                                .where(native_type: Link::NATIVE_TYPE_MCP_SERVER)
                                .find_by(unique_permalink: params[:id])

    return e404_json unless @mcp_server
  end

  def authorize_user
    if @mcp_server.present?
      authorize @mcp_server
    else
      authorize Link
    end
  end

  def mcp_server_params
    params.require(:mcp_server).permit(
      :name, :description, :price_cents, :endpoint_url, :api_documentation, :github_url,
      supported_tools: [], server_metadata: {}
    )
  end
end

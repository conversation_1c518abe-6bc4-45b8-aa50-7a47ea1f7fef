# frozen_string_literal: true

class Api::Internal::McpServerHealthController < Api::Internal::BaseController
  before_action :authenticate_user!
  after_action :verify_authorized

  def dashboard
    authorize Link, :index?
    
    service = McpServer::HealthMonitoringService.new
    dashboard_data = service.get_health_dashboard_data
    
    render json: {
      success: true,
      data: dashboard_data
    }
  end

  def server_details
    authorize Link, :show?
    
    mcp_server_config_id = params[:id]
    service = McpServer::HealthMonitoringService.new
    
    begin
      server_details = service.get_server_health_details(mcp_server_config_id)
      
      render json: {
        success: true,
        data: server_details
      }
    rescue ActiveRecord::RecordNotFound
      render json: {
        success: false,
        error: "MCP server not found"
      }, status: :not_found
    end
  end

  def trigger_health_check
    authorize Link, :update?
    
    mcp_server_config_id = params[:id]
    
    begin
      config = McpServerConfig.find(mcp_server_config_id)
      
      # Ensure the user owns this server or is an admin
      unless config.link.user == current_seller || current_seller.admin?
        return render json: {
          success: false,
          error: "Unauthorized"
        }, status: :forbidden
      end
      
      config.schedule_health_check!
      
      render json: {
        success: true,
        message: "Health check scheduled successfully"
      }
    rescue ActiveRecord::RecordNotFound
      render json: {
        success: false,
        error: "MCP server not found"
      }, status: :not_found
    end
  end

  def health_history
    authorize Link, :show?
    
    mcp_server_config_id = params[:id]
    days = (params[:days] || 7).to_i.clamp(1, 30)
    
    begin
      config = McpServerConfig.find(mcp_server_config_id)
      
      # Ensure the user owns this server or is an admin
      unless config.link.user == current_seller || current_seller.admin?
        return render json: {
          success: false,
          error: "Unauthorized"
        }, status: :forbidden
      end
      
      start_date = days.days.ago
      health_checks = config.mcp_server_health_checks
                           .where(checked_at: start_date..Time.current)
                           .order(checked_at: :desc)
                           .limit(1000) # Limit to prevent huge responses
      
      render json: {
        success: true,
        data: {
          health_checks: health_checks.as_json,
          summary: config.mcp_server_health_checks.health_summary(start_date: start_date),
          server: {
            id: config.id,
            endpoint_url: config.endpoint_url,
            health_status: config.health_status,
            last_health_check_at: config.last_health_check_at
          }
        }
      }
    rescue ActiveRecord::RecordNotFound
      render json: {
        success: false,
        error: "MCP server not found"
      }, status: :not_found
    end
  end

  def update_server_status
    authorize Link, :update?
    
    mcp_server_config_id = params[:id]
    new_status = params[:status]
    
    unless %w[active inactive].include?(new_status)
      return render json: {
        success: false,
        error: "Invalid status. Must be 'active' or 'inactive'"
      }, status: :bad_request
    end
    
    begin
      config = McpServerConfig.find(mcp_server_config_id)
      
      # Ensure the user owns this server or is an admin
      unless config.link.user == current_seller || current_seller.admin?
        return render json: {
          success: false,
          error: "Unauthorized"
        }, status: :forbidden
      end
      
      config.update!(is_active: new_status == 'active')
      
      render json: {
        success: true,
        message: "Server status updated successfully",
        data: {
          id: config.id,
          is_active: config.is_active
        }
      }
    rescue ActiveRecord::RecordNotFound
      render json: {
        success: false,
        error: "MCP server not found"
      }, status: :not_found
    end
  end
end

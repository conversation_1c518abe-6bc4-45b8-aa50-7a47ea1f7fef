# frozen_string_literal: true

class Api::Internal::McpServerApiKeysController < Api::Internal::BaseController
  before_action :authenticate_user!
  after_action :verify_authorized

  def index
    authorize Link, :index?
    
    link_id = params[:link_id]
    link = link_id ? Link.find(link_id) : nil
    
    service = McpServer::ApiKeyService.new
    api_keys = service.list_api_keys_for_user(user: current_seller, link: link)
    
    render json: {
      success: true,
      data: {
        api_keys: api_keys,
        total_count: api_keys.length
      }
    }
  end

  def show
    api_key = McpServerApiKey.find(params[:id])
    authorize_api_key_access!(api_key)
    
    render json: {
      success: true,
      data: {
        api_key: api_key.as_json,
        usage_stats: api_key.usage_stats(days: 30)
      }
    }
  end

  def create
    authorize Link, :create?
    
    purchase = Purchase.find(params[:purchase_id])
    link = Link.find(params[:link_id])
    
    # Verify user owns the purchase
    unless purchase.user == current_seller
      return render json: {
        success: false,
        error: "Unauthorized to create API key for this purchase"
      }, status: :forbidden
    end
    
    service = McpServer::ApiKeyService.new
    result = service.create_for_purchase(
      purchase: purchase,
      link: link,
      name: params[:name],
      permissions: params[:permissions] || [],
      rate_limits: params[:rate_limits] || {}
    )
    
    if result[:success]
      render json: {
        success: true,
        data: result.except(:success),
        message: "API key created successfully"
      }
    else
      render json: {
        success: false,
        error: result[:error]
      }, status: :unprocessable_entity
    end
  end

  def update
    api_key = McpServerApiKey.find(params[:id])
    authorize_api_key_access!(api_key)
    
    update_params = params.permit(:name, permissions: [], rate_limits: {})
    
    if api_key.update(update_params)
      render json: {
        success: true,
        data: api_key.as_json,
        message: "API key updated successfully"
      }
    else
      render json: {
        success: false,
        error: api_key.errors.full_messages.join(", ")
      }, status: :unprocessable_entity
    end
  end

  def revoke
    api_key = McpServerApiKey.find(params[:id])
    authorize_api_key_access!(api_key)
    
    service = McpServer::ApiKeyService.new
    result = service.revoke_api_key(api_key_id: api_key.id, user: current_seller)
    
    if result[:success]
      render json: {
        success: true,
        message: result[:message]
      }
    else
      render json: {
        success: false,
        error: result[:error]
      }, status: :forbidden
    end
  end

  def suspend
    api_key = McpServerApiKey.find(params[:id])
    authorize_api_key_access!(api_key)
    
    service = McpServer::ApiKeyService.new
    result = service.suspend_api_key(api_key_id: api_key.id, user: current_seller)
    
    if result[:success]
      render json: {
        success: true,
        message: result[:message]
      }
    else
      render json: {
        success: false,
        error: result[:error]
      }, status: :forbidden
    end
  end

  def activate
    api_key = McpServerApiKey.find(params[:id])
    authorize_api_key_access!(api_key)
    
    service = McpServer::ApiKeyService.new
    result = service.activate_api_key(api_key_id: api_key.id, user: current_seller)
    
    if result[:success]
      render json: {
        success: true,
        message: result[:message]
      }
    else
      render json: {
        success: false,
        error: result[:error]
      }, status: :forbidden
    end
  end

  def usage_stats
    api_key = McpServerApiKey.find(params[:id])
    authorize_api_key_access!(api_key)
    
    days = (params[:days] || 30).to_i.clamp(1, 365)
    usage_stats = api_key.usage_stats(days: days)
    
    render json: {
      success: true,
      data: {
        api_key_id: api_key.id,
        usage_stats: usage_stats,
        rate_limits: api_key.rate_limits,
        permissions: api_key.permissions
      }
    }
  end

  def regenerate
    api_key = McpServerApiKey.find(params[:id])
    authorize_api_key_access!(api_key)
    
    # Generate new API key
    new_api_key = McpServerApiKey.generate_key
    api_key.update!(api_key: new_api_key)
    
    render json: {
      success: true,
      data: {
        api_key: new_api_key,
        masked_key: api_key.masked_key
      },
      message: "API key regenerated successfully"
    }
  end

  private

  def authorize_api_key_access!(api_key)
    # User can access API keys for their own purchases or if they're the server owner
    unless api_key.purchase.user == current_seller || api_key.link.user == current_seller
      authorize Link, :admin? # This will fail and raise Pundit::NotAuthorizedError
    end
  end
end

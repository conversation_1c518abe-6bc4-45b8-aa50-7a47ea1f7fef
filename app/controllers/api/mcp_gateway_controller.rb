# frozen_string_literal: true

class Api::McpGatewayController < ApplicationController
  include ActionController::Live

  before_action :authenticate_api_key!
  before_action :check_rate_limits!
  before_action :validate_server_access!

  rescue_from McpServer::ApiKeyService::InvalidApiKeyError, with: :handle_invalid_api_key
  rescue_from McpServer::ApiKeyService::RateLimitExceededError, with: :handle_rate_limit_exceeded
  rescue_from McpServer::ApiKeyService::InsufficientPermissionsError, with: :handle_insufficient_permissions

  def proxy
    begin
      proxy_service = McpServer::ProxyService.new(
        mcp_server: @mcp_server,
        api_key_record: @api_key_record,
        request: request
      )

      result = proxy_service.proxy_request

      if result[:success]
        # Set response status and headers
        response.status = result[:status]
        result[:headers].each { |key, value| response.headers[key] = value }

        # Add rate limit headers
        add_rate_limit_headers

        render plain: result[:body]
      else
        render json: {
          error: "Proxy error",
          message: "Failed to process request"
        }, status: :bad_gateway
      end

    rescue McpServer::ProxyService::ServerUnavailableError => e
      render json: {
        error: "Server unavailable",
        message: e.message
      }, status: :service_unavailable

    rescue McpServer::ProxyService::TimeoutError => e
      render json: {
        error: "Request timeout",
        message: e.message
      }, status: :gateway_timeout

    rescue McpServer::ProxyService::RateLimitError => e
      add_rate_limit_headers
      render json: {
        error: "Rate limit exceeded",
        message: e.message
      }, status: :too_many_requests

    rescue StandardError => e
      Rails.logger.error "MCP Gateway error: #{e.message}"
      Bugsnag.notify(e, {
        api_key_id: @api_key_record.id,
        mcp_server_id: @mcp_server.id,
        request_path: request.path
      }) if defined?(Bugsnag)

      render json: {
        error: "Internal server error",
        message: "Failed to process request"
      }, status: :internal_server_error
    end
  end

  def status
    server_status = McpServer::ProxyService.get_server_status(@mcp_server)
    rate_limits = McpServer::RateLimiterService.new(@api_key_record).get_rate_limits_summary

    render json: {
      server: server_status,
      rate_limits: rate_limits,
      api_key: {
        id: @api_key_record.id,
        permissions: @api_key_record.permissions,
        expires_at: @api_key_record.expires_at
      }
    }
  end

  private

  def authenticate_api_key!
    api_key = extract_api_key_from_request

    unless api_key
      render json: { error: "Missing API key" }, status: :unauthorized
      return
    end

    server_id = params[:server_id]
    @mcp_server = Link.joins(:mcp_server_config)
                     .where(native_type: Link::NATIVE_TYPE_MCP_SERVER)
                     .find_by(external_id: server_id)

    unless @mcp_server
      render json: { error: "MCP server not found" }, status: :not_found
      return
    end

    service = McpServer::ApiKeyService.new(api_key)
    @api_key_record = service.authenticate_and_authorize(
      link: @mcp_server,
      permission: extract_required_permission
    )
  end

  def check_rate_limits!
    return unless @api_key_record

    rate_limiter = McpServer::RateLimiterService.new(@api_key_record)

    # Check hourly rate limit
    hourly_check = rate_limiter.check_rate_limit(window: 1.hour)
    unless hourly_check[:allowed]
      response.headers['X-RateLimit-Limit'] = hourly_check[:limit].to_s
      response.headers['X-RateLimit-Remaining'] = '0'
      response.headers['X-RateLimit-Reset'] = hourly_check[:reset_at].to_i.to_s
      response.headers['Retry-After'] = hourly_check[:retry_after].to_s

      raise McpServer::ApiKeyService::RateLimitExceededError, "Hourly rate limit exceeded"
    end

    # Check daily rate limit
    daily_check = rate_limiter.check_rate_limit(window: 1.day)
    unless daily_check[:allowed]
      raise McpServer::ApiKeyService::RateLimitExceededError, "Daily rate limit exceeded"
    end
  end

  def add_rate_limit_headers
    return unless @api_key_record

    rate_limiter = McpServer::RateLimiterService.new(@api_key_record)
    hourly_usage = rate_limiter.get_current_usage(window: 1.hour)

    response.headers['X-RateLimit-Limit'] = hourly_usage[:limit].to_s
    response.headers['X-RateLimit-Remaining'] = hourly_usage[:remaining].to_s
    response.headers['X-RateLimit-Reset'] = hourly_usage[:reset_at].to_i.to_s
  end

  def validate_server_access!
    return unless @mcp_server

    config = @mcp_server.mcp_server_config

    unless config.is_active?
      render json: {
        error: "Server unavailable",
        message: "This MCP server is currently inactive"
      }, status: :service_unavailable
      return
    end

    unless config.healthy?
      render json: {
        error: "Server unhealthy",
        message: "This MCP server is currently experiencing issues"
      }, status: :service_unavailable
      return
    end
  end



  def extract_api_key_from_request
    # Try multiple ways to get the API key
    request.headers['Authorization']&.sub(/^Bearer\s+/, '') ||
      request.headers['X-API-Key'] ||
      params[:api_key]
  end

  def extract_required_permission
    # Map HTTP methods to permissions
    case request.method.upcase
    when 'GET' then 'read'
    when 'POST', 'PUT', 'PATCH' then 'execute'
    when 'DELETE' then 'admin'
    else 'read'
    end
  end



  def handle_invalid_api_key(exception)
    render json: {
      error: "Invalid API key",
      message: exception.message
    }, status: :unauthorized
  end

  def handle_rate_limit_exceeded(exception)
    render json: {
      error: "Rate limit exceeded",
      message: exception.message,
      retry_after: 3600 # 1 hour
    }, status: :too_many_requests
  end

  def handle_insufficient_permissions(exception)
    render json: {
      error: "Insufficient permissions",
      message: exception.message
    }, status: :forbidden
  end
end

# frozen_string_literal: true

class McpServersController < Sellers::BaseController
  include ProductsHelper

  before_action :set_body_id_as_app
  before_action :set_title

  PER_PAGE = 50

  def index
    authorize Link
    create_user_event("mcp_servers_view")

    @guid = SecureRandom.hex
    @title = "MCP Servers"

    pagination, mcp_servers = paginated_mcp_servers(page: 1)

    @react_mcp_servers_page_props = DashboardMcpServersPagePresenter.new(
      pundit_user:,
      mcp_servers: mcp_servers,
      mcp_servers_pagination: pagination
    ).page_props
  end

  def mcp_servers_paged
    authorize Link, :index?

    pagination, mcp_servers = paginated_mcp_servers(page: paged_params[:page].to_i, query: params[:query])
    react_mcp_servers_page_props = DashboardMcpServersPagePresenter.new(
      pundit_user:,
      mcp_servers: mcp_servers,
      mcp_servers_pagination: pagination
    ).table_props

    render json: {
      pagination: react_mcp_servers_page_props[:mcp_servers_pagination],
      entries: react_mcp_servers_page_props[:mcp_servers]
    }
  end

  private

  def set_title
    @title = "MCP Servers"
  end

  def paginated_mcp_servers(page:, query: nil)
    sort_and_paginate_mcp_servers(
      collection: mcp_servers_scope(query),
      user_id: current_seller.id,
      key: paged_params[:sort_key],
      direction: paged_params[:sort_direction],
      page: page,
      per_page: PER_PAGE
    )
  end

  def mcp_servers_scope(query = nil)
    scope = current_seller.links
                          .joins(:mcp_server_config)
                          .where(native_type: Link::NATIVE_TYPE_MCP_SERVER)
                          .visible
                          .includes(:mcp_server_config, :mcp_server_usages)

    if query.present?
      scope = scope.where("links.name LIKE ? OR links.description LIKE ?", "%#{query}%", "%#{query}%")
    end

    scope
  end

  def sort_and_paginate_mcp_servers(collection:, user_id:, key: nil, direction: nil, page: 1, per_page: PER_PAGE)
    direction = direction == "desc" ? "desc" : "asc"
    page = 1 if page.to_i <= 0

    # For now, use simple sorting since MCP servers don't have elasticsearch integration yet
    sorted_collection = case key
    when "name"
      collection.order("links.name #{direction}")
    when "created_at"
      collection.order("links.created_at #{direction}")
    when "price"
      collection.order("links.price_cents #{direction}")
    when "health_status"
      collection.joins(:mcp_server_config).order("mcp_server_configs.health_status #{direction}")
    else
      collection.order(created_at: :desc)
    end

    pagination, mcp_servers = pagy(sorted_collection, limit: per_page, page: page)
    [PagyPresenter.new(pagination).props, mcp_servers]
  end

  def paged_params
    params.permit(:page, :sort_key, :sort_direction, :query)
  end
end

# frozen_string_literal: true

class McpGatewayMiddleware
  def initialize(app)
    @app = app
  end

  def call(env)
    request = ActionDispatch::Request.new(env)
    
    # Only process MCP gateway requests
    return @app.call(env) unless mcp_gateway_request?(request)
    
    start_time = Time.current
    
    # Apply global rate limiting
    rate_limit_result = apply_global_rate_limiting(request)
    unless rate_limit_result[:allowed]
      return rate_limit_response(rate_limit_result)
    end
    
    # Process the request
    status, headers, response = @app.call(env)
    
    # Log the request
    log_gateway_request(request, status, start_time)
    
    [status, headers, response]
    
  rescue StandardError => e
    Rails.logger.error "MCP Gateway Middleware error: #{e.message}"
    log_gateway_request(request, 500, start_time, error: e)
    
    [500, { 'Content-Type' => 'application/json' }, [
      { error: "Gateway error", message: "Internal server error" }.to_json
    ]]
  end

  private

  def mcp_gateway_request?(request)
    request.path.start_with?('/api/mcp/')
  end

  def apply_global_rate_limiting(request)
    # Apply global rate limiting per IP address
    ip_address = request.remote_ip
    
    # More aggressive rate limiting for unauthenticated requests
    if request.headers['Authorization'].blank? && request.headers['X-API-Key'].blank?
      McpServer::RateLimiterService.check_global_rate_limit(
        ip_address, 
        window: 1.minute, 
        limit: 20
      )
    else
      McpServer::RateLimiterService.check_global_rate_limit(
        ip_address, 
        window: 1.minute, 
        limit: 100
      )
    end
  end

  def rate_limit_response(rate_limit_result)
    headers = {
      'Content-Type' => 'application/json',
      'X-RateLimit-Limit' => rate_limit_result[:limit].to_s,
      'X-RateLimit-Remaining' => '0',
      'X-RateLimit-Reset' => rate_limit_result[:reset_at].to_i.to_s,
      'Retry-After' => rate_limit_result[:retry_after].to_s
    }
    
    body = {
      error: "Rate limit exceeded",
      message: "Too many requests from this IP address",
      retry_after: rate_limit_result[:retry_after]
    }.to_json
    
    [429, headers, [body]]
  end

  def log_gateway_request(request, status, start_time, error: nil)
    duration_ms = ((Time.current - start_time) * 1000).round(2)
    
    log_data = {
      timestamp: Time.current.iso8601,
      method: request.method,
      path: request.path,
      query_string: request.query_string,
      remote_ip: request.remote_ip,
      user_agent: request.user_agent,
      status: status,
      duration_ms: duration_ms,
      has_api_key: request.headers['Authorization'].present? || request.headers['X-API-Key'].present?
    }
    
    if error
      log_data[:error] = {
        message: error.message,
        class: error.class.name
      }
    end
    
    # Log to Rails logger
    Rails.logger.info "MCP Gateway: #{log_data.to_json}"
    
    # Could also send to external logging service here
    # LoggingService.log_gateway_request(log_data) if defined?(LoggingService)
  end
end

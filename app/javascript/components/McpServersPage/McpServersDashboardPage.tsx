import * as React from "react";
import { McpServer, PaginationProps } from "$app/data/mcp_servers";
import { McpServersTable } from "$app/components/McpServersPage/McpServersTable";
import { Icon } from "$app/components/Icons";
import { Button } from "$app/components/Button";
import { NavigationButton } from "$app/components/Button";
import { Routes } from "$app/utils/routes";
import { Popover } from "$app/components/Popover";
import { WithTooltip } from "$app/components/WithTooltip";

const placeholder = "/assets/placeholder-image.svg"; // You may need to adjust this path

export const McpServersDashboardPage = ({
  mcp_servers: mcpServers,
  mcp_servers_pagination: mcpServersPagination,
  can_create_mcp_server: canCreateMcpServer,
}: {
  mcp_servers: McpServer[];
  mcp_servers_pagination: PaginationProps;
  can_create_mcp_server: boolean;
}) => {
  const searchInputRef = React.useRef<HTMLInputElement>(null);
  const [isSearchPopoverOpen, setIsSearchPopoverOpen] = React.useState(false);
  const [query, setQuery] = React.useState<string | null>(null);

  const handleSearch = (searchQuery: string) => {
    setQuery(searchQuery.trim() || null);
  };

  const clearSearch = () => {
    setQuery(null);
    if (searchInputRef.current) {
      searchInputRef.current.value = "";
    }
  };

  return (
    <main className="dashboard-page">
      <header className="dashboard-header">
        <div className="header-content">
          <h1>MCP Servers</h1>
          <div className="header-actions">
            {canCreateMcpServer && (
              <NavigationButton href={Routes.new_internal_mcp_server_path()} color="accent">
                <Icon name="plus" />
                New MCP Server
              </NavigationButton>
            )}
          </div>
        </div>
      </header>

      <section className="dashboard-content">
        {mcpServers.length === 0 && !query ? (
          <div className="placeholder">
            <figure>
              <img src={placeholder} alt="No MCP servers" />
            </figure>
            <h2>Ready to monetize your AI tools?</h2>
            <p>Create your first MCP server to start offering AI tools and services to the community.</p>
            <div className="placeholder-features">
              <div className="feature">
                <Icon name="globe" />
                <h3>Import from GitHub</h3>
                <p>Already have an MCP server? Import it directly from your GitHub repository.</p>
              </div>
              <div className="feature">
                <Icon name="lightning-fill" />
                <h3>Generate with AI</h3>
                <p>Coming soon - Let AI help you create a custom MCP server.</p>
              </div>
              <div className="feature">
                <Icon name="currency-dollar" />
                <h3>Monetize Your Tools</h3>
                <p>Set pricing and start earning from your AI tools and services.</p>
              </div>
            </div>
            {canCreateMcpServer && (
              <div>
                <NavigationButton href={Routes.new_internal_mcp_server_path()} color="accent">
                  Create your first MCP server
                </NavigationButton>
              </div>
            )}
            <span>
              or <a data-helper-prompt="Can you tell me more about MCP servers?">learn more about MCP servers</a>
            </span>
          </div>
        ) : (
          <div className="table-section">
            <div className="table-header">
              <div className="table-title">
                <h2>Your MCP Servers</h2>
                {query && <span className="search-results">Search results for "{query}"</span>}
              </div>
              <div className="table-actions">
                <Popover
                  open={isSearchPopoverOpen}
                  onToggle={setIsSearchPopoverOpen}
                  aria-label="Toggle Search"
                  trigger={
                    <WithTooltip tip="Search" position="bottom">
                      <div className="button">
                        <Icon name="solid-search" />
                      </div>
                    </WithTooltip>
                  }
                >
                  <div className="input">
                    <Icon name="solid-search" />
                    <input
                      ref={searchInputRef}
                      type="text"
                      placeholder="Search MCP servers..."
                      value={query ?? ""}
                      onChange={(evt) => setQuery(evt.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          handleSearch(e.currentTarget.value);
                          setIsSearchPopoverOpen(false);
                        }
                      }}
                    />
                  </div>
                </Popover>
              </div>
            </div>

            <McpServersTable entries={mcpServers} pagination={mcpServersPagination} query={query} />

            {mcpServers.length === 0 && query && (
              <div className="empty-search-results">
                <Icon name="search" />
                <h3>No MCP servers found</h3>
                <p>Try adjusting your search terms or create a new MCP server.</p>
                <Button onClick={clearSearch}>Clear search</Button>
              </div>
            )}
          </div>
        )}
      </section>
    </main>
  );
};

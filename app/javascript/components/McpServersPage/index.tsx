import * as React from "react";
import { McpServersDashboardPage } from "$app/components/McpServersPage/McpServersDashboardPage";
import { McpServer, PaginationProps } from "$app/data/mcp_servers";

export type McpServersPageProps = {
  mcp_servers: McpServer[];
  mcp_servers_pagination: PaginationProps;
  can_create_mcp_server: boolean;
};

export const McpServersPage = (props: McpServersPageProps) => {
  return <McpServersDashboardPage {...props} />;
};

export default McpServersPage;

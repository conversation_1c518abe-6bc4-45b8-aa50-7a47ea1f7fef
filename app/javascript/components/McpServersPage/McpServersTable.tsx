import * as React from "react";
import { AbortError, assertResponseError } from "$app/utils/errors";
import { showAlert } from "$app/utils/alerts";
import { getPagedMcpServers, McpServer, McpServersParams, PaginationProps } from "$app/data/mcp_servers";
import { useSortingTableDriver, Sort } from "$app/hooks/useSortingTableDriver";
import { asyncVoid } from "$app/utils/async";
import { Icon } from "$app/components/Icons";
import { Button } from "$app/components/Button";
import { Pagination } from "$app/components/Pagination";
import { LoadingSpinner } from "$app/components/LoadingSpinner";
import { Popover } from "$app/components/Popover";
import { Routes } from "$app/utils/routes";

type SortKey = "name" | "created_at" | "price" | "health_status";

type State = {
  entries: McpServer[];
  pagination: PaginationProps;
  isLoading: boolean;
  query: string | null;
};

export const McpServersPageTable = (props: {
  entries: McpServer[];
  pagination: PaginationProps;
  query: string | null;
}) => {
  const [{ entries: mcpServers, pagination, isLoading }, setState] = React.useState<State>({
    entries: props.entries,
    pagination: props.pagination,
    isLoading: false,
  });
  const activeRequest = React.useRef<{ cancel: () => void } | null>(null);
  const tableRef = React.useRef<HTMLTableElement>(null);

  const [sort, setSort] = React.useState<Sort<SortKey> | null>(null);
  const thProps = useSortingTableDriver<SortKey>(sort, setSort);

  React.useEffect(() => {
    if (sort) void loadMcpServers(1);
  }, [sort]);

  const loadMcpServers = async (page: number) => {
    setState((prevState) => ({ ...prevState, isLoading: true }));
    try {
      activeRequest.current?.cancel();

      const request = getPagedMcpServers({
        page,
        query: props.query,
        sort,
      });
      activeRequest.current = request;

      const response = await request.response;
      setState((prevState) => ({
        ...prevState,
        ...response,
        isLoading: false,
      }));
      activeRequest.current = null;
      tableRef.current?.scrollIntoView({ behavior: "smooth" });
    } catch (e) {
      if (e instanceof AbortError) return;
      assertResponseError(e);
      setState((prevState) => ({ ...prevState, isLoading: false }));
      showAlert(e.message, "error");
    }
  };

  const handleDelete = async (mcpServer: McpServer) => {
    if (!confirm(`Are you sure you want to delete "${mcpServer.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(Routes.internal_mcp_server_path(mcpServer.id), {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": document.querySelector<HTMLMetaElement>('meta[name="csrf-token"]')?.content || "",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to delete MCP server");
      }

      showAlert("MCP server deleted successfully!", "success");
      void loadMcpServers(pagination.page);
    } catch (error) {
      showAlert("Failed to delete MCP server", "error");
    }
  };

  const formatHealthStatus = (status: string) => {
    const statusMap = {
      healthy: { text: "Healthy", color: "text-green-600" },
      unhealthy: { text: "Unhealthy", color: "text-red-600" },
      unknown: { text: "Unknown", color: "text-gray-500" },
    };
    return statusMap[status as keyof typeof statusMap] || statusMap.unknown;
  };

  const formatUsageStats = (mcpServer: McpServer) => {
    if (mcpServer.total_requests === 0) {
      return "No usage";
    }
    return `${mcpServer.total_requests} requests (${mcpServer.success_rate}% success)`;
  };

  return (
    <div className="table-container">
      {isLoading && <LoadingSpinner />}
      <table ref={tableRef} className="table">
        <thead>
          <tr>
            <th {...thProps("name")}>
              <button type="button">
                Name
                <Icon name="chevron-up-down" />
              </button>
            </th>
            <th {...thProps("price")}>
              <button type="button">
                Price
                <Icon name="chevron-up-down" />
              </button>
            </th>
            <th {...thProps("health_status")}>
              <button type="button">
                Health
                <Icon name="chevron-up-down" />
              </button>
            </th>
            <th>Usage Stats</th>
            <th>Status</th>
            <th {...thProps("created_at")}>
              <button type="button">
                Created
                <Icon name="chevron-up-down" />
              </button>
            </th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {mcpServers.map((mcpServer) => {
            const healthStatus = formatHealthStatus(mcpServer.health_status);
            return (
              <tr key={mcpServer.id}>
                <td>
                  <div className="flex flex-col">
                    <a href={mcpServer.url} className="font-medium text-blue-600 hover:text-blue-800">
                      {mcpServer.name}
                    </a>
                    {mcpServer.description && (
                      <span className="text-gray-500 max-w-xs truncate text-sm">{mcpServer.description}</span>
                    )}
                  </div>
                </td>
                <td>{mcpServer.price_formatted}</td>
                <td>
                  <span className={healthStatus.color}>{healthStatus.text}</span>
                </td>
                <td>
                  <div className="text-sm">
                    {formatUsageStats(mcpServer)}
                    {mcpServer.average_response_time_ms > 0 && (
                      <div className="text-gray-500">Avg: {mcpServer.average_response_time_ms}ms</div>
                    )}
                  </div>
                </td>
                <td>
                  <span
                    className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${
                      mcpServer.published ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"
                    }`}
                  >
                    {mcpServer.published ? "Published" : "Unpublished"}
                  </span>
                </td>
                <td>{new Date(mcpServer.created_at).toLocaleDateString()}</td>
                <td>
                  <Popover aria-label="Open MCP server action menu" trigger={<Icon name="three-dots" />}>
                    <div role="menu">
                      {mcpServer.can_edit && (
                        <div role="menuitem">
                          <a href={mcpServer.edit_url}>
                            <Icon name="pencil" />
                            &ensp;Edit
                          </a>
                        </div>
                      )}
                      {mcpServer.can_duplicate && (
                        <div
                          role="menuitem"
                          onClick={() => {
                            showAlert("Duplication feature coming soon!", "info");
                          }}
                        >
                          <Icon name="outline-duplicate" />
                          &ensp;Duplicate
                        </div>
                      )}
                      {mcpServer.can_destroy && (
                        <div className="danger" role="menuitem" onClick={() => handleDelete(mcpServer)}>
                          <Icon name="trash2" />
                          &ensp;Delete permanently
                        </div>
                      )}
                    </div>
                  </Popover>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>

      {mcpServers.length === 0 && !isLoading && (
        <div className="text-gray-500 py-8 text-center">No MCP servers found.</div>
      )}

      {pagination.pages > 1 && (
        <div className="mt-6">
          <Pagination pagination={pagination} onChangePage={loadMcpServers} />
        </div>
      )}
    </div>
  );
};

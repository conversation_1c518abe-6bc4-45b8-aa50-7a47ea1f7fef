# frozen_string_literal: true

class DashboardMcpServersPagePresenter
  include ActionView::Helpers::NumberHelper
  include ActionView::Helpers::TextHelper
  include Rails.application.routes.url_helpers

  attr_reader :mcp_servers, :mcp_servers_pagination, :pundit_user

  def initialize(pundit_user:, mcp_servers:, mcp_servers_pagination:)
    @pundit_user = pundit_user
    @mcp_servers = mcp_servers
    @mcp_servers_pagination = mcp_servers_pagination
  end

  def page_props
    {
      mcp_servers: mcp_servers_data,
      mcp_servers_pagination:,
      can_create_mcp_server: Pundit.policy!(@pundit_user, Link).create?,
    }
  end

  def table_props
    {
      mcp_servers: mcp_servers_data,
      mcp_servers_pagination:,
    }
  end

  private

  def mcp_servers_data
    mcp_servers.map do |mcp_server|
      mcp_server_base_data(mcp_server, pundit_user:)
    end
  end

  def mcp_server_base_data(mcp_server, pundit_user:)
    config = mcp_server.mcp_server_config
    usage_stats = calculate_usage_stats(mcp_server)

    policy = Pundit.policy!(pundit_user, mcp_server)

    {
      "id" => mcp_server.unique_permalink,
      "name" => mcp_server.name,
      "description" => mcp_server.description,
      "endpoint_url" => config.endpoint_url,
      "price_formatted" => format_price(mcp_server),
      "display_price_cents" => mcp_server.price_cents,
      "health_status" => config.health_status,
      "health_status_display" => health_status_display(config.health_status),
      "last_health_check_at" => config.last_health_check_at&.iso8601,
      "is_active" => config.is_active,
      "status" => mcp_server.published? ? "published" : "unpublished",
      "created_at" => mcp_server.created_at.iso8601,
      "updated_at" => mcp_server.updated_at.iso8601,
      "supported_tools" => config.supported_tools,
      "github_url" => config.github_url,
      "total_requests" => usage_stats[:total_requests],
      "successful_requests" => usage_stats[:successful_requests],
      "failed_requests" => usage_stats[:failed_requests],
      "success_rate" => usage_stats[:success_rate],
      "average_response_time_ms" => usage_stats[:average_response_time_ms],
      "published" => mcp_server.published?,
      "url" => mcp_server.long_url,
      "edit_url" => edit_internal_mcp_server_path(mcp_server.unique_permalink),
      "can_edit" => policy.update?,
      "can_destroy" => policy.destroy?,
      "can_duplicate" => policy.create?,
      "can_archive" => false, # MCP servers don't support archiving yet
      "can_unarchive" => false,
      "is_duplicating" => false, # TODO: Implement duplication tracking
      "is_unpublished" => !mcp_server.published?,
      "thumbnail" => nil, # MCP servers don't have thumbnails
      "permalink" => mcp_server.unique_permalink,
      "url_without_protocol" => mcp_server.long_url.gsub(/^https?:\/\//, ""),
      "revenue" => 0.0, # TODO: Calculate revenue from purchases
      "revenue_pending" => 0.0,
      "total_usd_cents" => 0,
      "successful_sales_count" => 0, # TODO: Calculate from purchases
      "remaining_for_sale_count" => nil,
      "monthly_recurring_revenue" => 0.0,
      "has_duration" => false
    }
  end

  def format_price(mcp_server)
    Money.new(mcp_server.price_cents, pundit_user.seller.currency_type).format(
      no_cents_if_whole: true
    )
  end

  def health_status_display(status)
    case status
    when "healthy"
      "Healthy"
    when "unhealthy"
      "Unhealthy"
    when "unknown"
      "Unknown"
    else
      "Unknown"
    end
  end

  def calculate_usage_stats(mcp_server)
    usages = mcp_server.mcp_server_usages.where(usage_date: 30.days.ago..Date.current)

    total_requests = usages.sum(:total_requests)
    successful_requests = usages.sum(:successful_requests)
    failed_requests = usages.sum(:failed_requests)

    success_rate = total_requests > 0 ? ((successful_requests.to_f / total_requests) * 100).round(1) : 0.0

    # Calculate weighted average response time
    total_response_time = usages.sum("total_response_time_ms")
    average_response_time_ms = total_requests > 0 ? (total_response_time / total_requests).round(2) : 0.0

    {
      total_requests: total_requests,
      successful_requests: successful_requests,
      failed_requests: failed_requests,
      success_rate: success_rate,
      average_response_time_ms: average_response_time_ms
    }
  end
end

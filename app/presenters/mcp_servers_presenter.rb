# frozen_string_literal: true

class McpServersPresenter
  include ProductsHelper
  include Rails.application.routes.url_helpers

  PER_PAGE = 50

  def initialize(seller:, page: 1, query: nil)
    @seller = seller
    @page = page
    @query = query
  end

  def index_props
    {
      mcp_servers: mcp_servers_data,
      pagination: pagination_data
    }
  end

  private

  attr_reader :seller, :page, :query

  def mcp_servers_scope
    scope = seller.links
                  .joins(:mcp_server_config)
                  .where(native_type: Link::NATIVE_TYPE_MCP_SERVER)
                  .visible
                  .includes(:mcp_server_config, :mcp_server_usages)

    if query.present?
      scope = scope.where("links.name ILIKE ? OR mcp_server_configs.description ILIKE ?", "%#{query}%", "%#{query}%")
    end

    scope.order(created_at: :desc)
  end

  def paginated_mcp_servers
    @paginated_mcp_servers ||= mcp_servers_scope.page(page).per(PER_PAGE)
  end

  def mcp_servers_data
    paginated_mcp_servers.map do |link|
      config = link.mcp_server_config
      usage_stats = calculate_usage_stats(link)

      {
        id: link.unique_permalink,
        name: link.name,
        description: link.description,
        endpoint_url: config.endpoint_url,
        price_cents: link.price_cents,
        currency_code: seller.currency_code,
        health_status: config.health_status,
        last_health_check_at: config.last_health_check_at&.iso8601,
        is_active: config.is_active,
        created_at: link.created_at.iso8601,
        updated_at: link.updated_at.iso8601,
        supported_tools: config.supported_tools,
        github_url: config.github_url,
        total_requests: usage_stats[:total_requests],
        successful_requests: usage_stats[:successful_requests],
        failed_requests: usage_stats[:failed_requests],
        average_response_time_ms: usage_stats[:average_response_time_ms],
        published: link.published?
      }
    end
  end

  def pagination_data
    {
      current_page: paginated_mcp_servers.current_page,
      total_pages: paginated_mcp_servers.total_pages,
      total_count: paginated_mcp_servers.total_count
    }
  end

  def calculate_usage_stats(link)
    usages = link.mcp_server_usages.where(usage_date: 30.days.ago..Date.current)

    total_requests = usages.sum(:total_requests)
    successful_requests = usages.sum(:successful_requests)
    failed_requests = usages.sum(:failed_requests)

    # Calculate weighted average response time
    total_response_time = usages.sum("total_response_time_ms")
    average_response_time_ms = total_requests > 0 ? (total_response_time / total_requests).round(2) : 0.0

    {
      total_requests: total_requests,
      successful_requests: successful_requests,
      failed_requests: failed_requests,
      average_response_time_ms: average_response_time_ms
    }
  end
end

# frozen_string_literal: true

class PublicMcpServerPresenter
  include ProductsHelper
  include Rails.application.routes.url_helpers

  def initialize(mcp_server:, request: nil, recommended_by: nil, target: nil, query: nil)
    @mcp_server = mcp_server
    @request = request
    @recommended_by = recommended_by
    @target = target
    @query = query
  end

  def card_props
    config = mcp_server.mcp_server_config
    usage_stats = calculate_usage_stats

    {
      id: mcp_server.external_id,
      name: mcp_server.name,
      description: mcp_server.description,
      endpoint_url: config.endpoint_url,
      price_cents: mcp_server.price_cents,
      currency_code: mcp_server.user.currency_code,
      health_status: config.health_status,
      last_health_check_at: config.last_health_check_at&.iso8601,
      is_active: config.is_active,
      created_at: mcp_server.created_at.iso8601,
      updated_at: mcp_server.updated_at.iso8601,
      supported_tools: config.supported_tools,
      github_url: config.github_url,
      total_requests: usage_stats[:total_requests],
      successful_requests: usage_stats[:successful_requests],
      failed_requests: usage_stats[:failed_requests],
      average_response_time_ms: usage_stats[:average_response_time_ms],
      success_rate: usage_stats[:success_rate],
      published: mcp_server.published?,
      seller: {
        id: mcp_server.user.external_id,
        name: mcp_server.user.name || mcp_server.user.username,
        username: mcp_server.user.username,
        profile_url: mcp_server.user.profile_url
      },
      url: mcp_server.long_url,
      purchase_url: mcp_server.long_url,
      tags: mcp_server.tags.pluck(:name),
      server_metadata: config.server_metadata,
      recommended_by: recommended_by,
      target: target,
      query: query
    }
  end

  def detail_props
    card_props.merge(
      api_documentation: mcp_server.mcp_server_config.api_documentation,
      detailed_usage_stats: detailed_usage_stats,
      reviews: reviews_data,
      related_servers: related_servers_data
    )
  end

  private

  attr_reader :mcp_server, :request, :recommended_by, :target, :query

  def calculate_usage_stats
    usages = mcp_server.mcp_server_usages.where(usage_date: 30.days.ago..Date.current)

    total_requests = usages.sum(:total_requests)
    successful_requests = usages.sum(:successful_requests)
    failed_requests = usages.sum(:failed_requests)

    success_rate = total_requests > 0 ? (successful_requests.to_f / total_requests * 100).round(2) : 0.0

    # Calculate weighted average response time
    total_response_time = usages.sum("total_response_time_ms * total_requests")
    average_response_time_ms = total_requests > 0 ? (total_response_time / total_requests).round(2) : 0.0

    {
      total_requests: total_requests,
      successful_requests: successful_requests,
      failed_requests: failed_requests,
      average_response_time_ms: average_response_time_ms,
      success_rate: success_rate
    }
  end

  def detailed_usage_stats
    # Get usage stats for the last 7 days for charting
    daily_stats = mcp_server.mcp_server_usages
                            .where(usage_date: 7.days.ago..Date.current)
                            .group(:usage_date)
                            .sum(:total_requests)

    {
      daily_requests: daily_stats,
      uptime_percentage: calculate_uptime_percentage,
      average_response_time_trend: calculate_response_time_trend
    }
  end

  def calculate_uptime_percentage
    # Calculate actual uptime based on health check records
    config = mcp_server.mcp_server_config
    uptime = config.uptime_percentage(days: 7)

    # Fallback to status-based calculation if no health checks exist
    return uptime if uptime > 0

    case config.health_status
    when "healthy" then 100.0
    when "unhealthy" then 0.0
    else 50.0 # unknown status
    end
  end

  def calculate_response_time_trend
    # Get average response times from health checks for the last 7 days
    config = mcp_server.mcp_server_config

    (0..6).map do |days_ago|
      date = days_ago.days.ago.to_date
      start_time = date.beginning_of_day
      end_time = date.end_of_day

      avg_response_time = config.mcp_server_health_checks
                               .successful
                               .where(checked_at: start_time..end_time)
                               .average(:response_time_ms)&.round(2) || 0.0

      [date.iso8601, avg_response_time]
    end.reverse.to_h
  end

  def reviews_data
    # Placeholder for reviews - would need to implement review system
    {
      average_rating: 0.0,
      total_reviews: 0,
      reviews: []
    }
  end

  def related_servers_data
    # Find related MCP servers based on supported tools
    config = mcp_server.mcp_server_config
    return [] if config.supported_tools.empty?

    related_servers = Link.joins(:mcp_server_config)
                          .where(native_type: Link::NATIVE_TYPE_MCP_SERVER)
                          .where.not(id: mcp_server.id)
                          .published
                          .alive
                          .limit(3)

    related_servers.map do |server|
      PublicMcpServerPresenter.new(
        mcp_server: server,
        request: request,
        target: target
      ).card_props
    end
  end
end

# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'MCP Servers Listing', type: :feature, js: true do
  let(:user) { create(:user) }
  let!(:mcp_server1) { create(:link, :mcp_server, user: user, name: "File Operations Server") }
  let!(:mcp_server_config1) { create(:mcp_server_config, link: mcp_server1, health_status: "healthy") }
  let!(:mcp_server2) { create(:link, :mcp_server, user: user, name: "Web Search Server") }
  let!(:mcp_server_config2) { create(:mcp_server_config, link: mcp_server2, health_status: "unhealthy") }

  before do
    sign_in user
  end

  describe "MCP Servers index page" do
    it "displays the MCP servers listing page" do
      visit mcp_servers_path
      
      expect(page).to have_content("MCP Servers")
      expect(page).to have_content("File Operations Server")
      expect(page).to have_content("Web Search Server")
    end

    it "shows server health status" do
      visit mcp_servers_path
      
      # Wait for React component to load
      expect(page).to have_content("File Operations Server")
      
      # Check that health status is displayed (this will depend on the actual React component rendering)
      # The exact selectors may need to be adjusted based on the final implementation
    end

    it "allows searching for servers" do
      visit mcp_servers_path
      
      # Wait for the page to load
      expect(page).to have_content("File Operations Server")
      
      # This test would need to be expanded once the search functionality is fully implemented
      # and we can interact with the React components
    end
  end

  describe "Navigation" do
    it "includes MCP Servers in the main navigation" do
      visit dashboard_path
      
      within "nav[aria-label='Main']" do
        expect(page).to have_link("MCP Servers", href: mcp_servers_path)
      end
    end
  end

  describe "Authorization" do
    context "when user is not signed in" do
      before { sign_out user }

      it "redirects to login" do
        visit mcp_servers_path
        expect(current_path).to eq(new_user_session_path)
      end
    end

    context "when user doesn't own the servers" do
      let(:other_user) { create(:user) }
      
      before do
        sign_out user
        sign_in other_user
      end

      it "doesn't show other users' servers" do
        visit mcp_servers_path
        
        expect(page).not_to have_content("File Operations Server")
        expect(page).not_to have_content("Web Search Server")
      end
    end
  end

  describe "Empty state" do
    let(:user_without_servers) { create(:user) }
    
    before do
      sign_out user
      sign_in user_without_servers
    end

    it "shows empty state when user has no MCP servers" do
      visit mcp_servers_path
      
      expect(page).to have_content("No MCP servers yet")
      expect(page).to have_content("Create your first MCP server")
    end
  end
end

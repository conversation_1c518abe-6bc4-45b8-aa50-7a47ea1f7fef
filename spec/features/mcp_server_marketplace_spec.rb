# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'MCP Server Marketplace', type: :feature do
  let(:seller) { create(:user, :seller) }
  let(:buyer) { create(:user) }
  let(:mcp_server) { create(:link, user: seller, native_type: Link::NATIVE_TYPE_MCP_SERVER, published: true) }
  let(:mcp_server_config) { create(:mcp_server_config, link: mcp_server, is_active: true, health_status: 'healthy') }

  before do
    mcp_server_config # Ensure the config exists
  end

  describe 'Seller workflow' do
    before do
      sign_in seller
    end

    scenario 'Seller creates and configures an MCP server' do
      visit new_link_path

      # Create MCP server
      fill_in 'Name', with: 'My Awesome MCP Server'
      fill_in 'Description', with: 'A powerful MCP server for file operations'
      select 'MCP Server', from: 'Product Type'
      fill_in 'Price', with: '29.99'

      # Configure MCP server settings
      fill_in 'Endpoint URL', with: 'https://my-server.com/api'
      fill_in 'API Documentation', with: 'This server provides file operation tools'
      fill_in 'GitHub Repository', with: 'https://github.com/me/my-mcp-server'
      fill_in 'Supported Tools', with: 'file_read, file_write, file_list'

      # Set pricing model
      choose 'Per-Request Pricing'
      fill_in 'Free Tier Requests', with: '100'
      fill_in 'Price per Request', with: '0.01'

      click_button 'Create Product'

      expect(page).to have_content('Product created successfully')
      expect(page).to have_content('My Awesome MCP Server')
      expect(page).to have_content('https://my-server.com/api')
    end

    scenario 'Seller views health monitoring dashboard' do
      visit mcp_servers_path

      click_link 'Health Dashboard'

      expect(page).to have_content('MCP Server Health Monitoring')
      expect(page).to have_content('Total Servers')
      expect(page).to have_content('Healthy Servers')
      expect(page).to have_content('Server Health Overview')
    end

    scenario 'Seller manages API keys for their server' do
      purchase = create(:purchase, user: buyer, link: mcp_server, successful: true)
      api_key = create(:mcp_server_api_key, purchase: purchase, link: mcp_server)

      visit link_path(mcp_server)
      click_link 'API Keys'

      expect(page).to have_content('API Key Management')
      expect(page).to have_content(api_key.name)
      expect(page).to have_content('Active')

      # Suspend API key
      within("[data-api-key-id='#{api_key.id}']") do
        click_button 'Suspend'
      end

      expect(page).to have_content('API key suspended successfully')
      expect(api_key.reload.suspended?).to be true
    end
  end

  describe 'Buyer workflow' do
    before do
      sign_in buyer
    end

    scenario 'Buyer discovers and purchases an MCP server' do
      visit discover_mcp_servers_path

      expect(page).to have_content('MCP Server Marketplace')
      expect(page).to have_content(mcp_server.name)
      expect(page).to have_content('Healthy')

      # View server details
      click_link mcp_server.name

      expect(page).to have_content(mcp_server.description)
      expect(page).to have_content('Endpoint URL')
      expect(page).to have_content('Supported Tools')

      # Purchase the server
      click_button 'Purchase'

      # Complete purchase flow (simplified)
      fill_in 'Email', with: buyer.email
      click_button 'Complete Purchase'

      expect(page).to have_content('Purchase successful')
      expect(page).to have_content('API Key')
    end

    scenario 'Buyer uses API key to access MCP server', :js do
      purchase = create(:purchase, user: buyer, link: mcp_server, successful: true)
      api_key = create(:mcp_server_api_key, purchase: purchase, link: mcp_server)

      # Mock the MCP server response
      stub_request(:get, "#{mcp_server_config.endpoint_url}/status")
        .with(headers: { 'Authorization' => "Bearer #{api_key.api_key}" })
        .to_return(status: 200, body: '{"status": "ok", "tools": ["file_read", "file_write"]}')

      visit purchase_path(purchase)

      expect(page).to have_content('API Key')
      expect(page).to have_content(api_key.masked_key)

      # Test API endpoint
      click_button 'Test Connection'

      expect(page).to have_content('Connection successful')
      expect(page).to have_content('Status: ok')
    end
  end

  describe 'API Gateway integration' do
    let(:purchase) { create(:purchase, user: buyer, link: mcp_server, successful: true) }
    let(:api_key) { create(:mcp_server_api_key, purchase: purchase, link: mcp_server) }

    scenario 'API requests are properly proxied and tracked' do
      # Mock the actual MCP server
      stub_request(:post, "#{mcp_server_config.endpoint_url}/search")
        .with(
          body: '{"query": "test"}',
          headers: {
            'Content-Type' => 'application/json',
            'User-Agent' => 'Gumroad-MCP-Gateway/1.0'
          }
        )
        .to_return(
          status: 200,
          body: '{"results": ["file1.txt", "file2.txt"]}',
          headers: { 'Content-Type' => 'application/json' }
        )

      # Make API request through gateway
      post "/api/mcp/#{mcp_server.external_id}/search",
           params: { query: 'test' }.to_json,
           headers: {
             'Authorization' => "Bearer #{api_key.api_key}",
             'Content-Type' => 'application/json'
           }

      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['results']).to eq(['file1.txt', 'file2.txt'])

      # Check that usage was recorded
      usage = McpServerUsage.last
      expect(usage.link).to eq(mcp_server)
      expect(usage.purchase).to eq(purchase)
      expect(usage.api_key_hash).to eq(api_key.key_hash)
      expect(usage.total_requests).to eq(1)
      expect(usage.successful_requests).to eq(1)
    end

    scenario 'Rate limiting is enforced' do
      # Set low rate limit
      api_key.update!(rate_limits: { 'requests_per_hour' => 2 })

      # Make requests up to the limit
      2.times do |i|
        post "/api/mcp/#{mcp_server.external_id}/test",
             headers: { 'Authorization' => "Bearer #{api_key.api_key}" }
        expect(response).to have_http_status(:ok)
      end

      # Next request should be rate limited
      post "/api/mcp/#{mcp_server.external_id}/test",
           headers: { 'Authorization' => "Bearer #{api_key.api_key}" }

      expect(response).to have_http_status(:too_many_requests)
      expect(JSON.parse(response.body)['error']).to eq('Rate limit exceeded')
      expect(response.headers['Retry-After']).to be_present
    end
  end

  describe 'Health monitoring' do
    scenario 'Health checks are performed and tracked' do
      # Simulate health check job
      McpServerHealthCheckJob.new.perform(mcp_server_config.id)

      health_check = mcp_server_config.mcp_server_health_checks.last
      expect(health_check).to be_present
      expect(health_check.endpoint_url).to eq(mcp_server_config.endpoint_url)

      # Check that health status is updated
      expect(mcp_server_config.reload.last_health_check_at).to be_present
    end

    scenario 'Unhealthy servers are marked and alerts are generated' do
      # Simulate failed health checks
      3.times do
        create(:mcp_server_health_check, 
               mcp_server_config: mcp_server_config, 
               success: false,
               checked_at: Time.current)
      end

      mcp_server_config.update!(health_status: 'unhealthy')

      expect(mcp_server_config.consecutive_failures).to eq(3)
      expect(mcp_server_config.should_alert_for_downtime?).to be true

      # Simulate alert processing
      expect(Rails.logger).to receive(:warn).with(/ALERT: MCP Server/)
      McpServer::HealthMonitoringService.process_health_alerts
    end
  end

  describe 'Pricing and billing' do
    let(:purchase) { create(:purchase, user: buyer, link: mcp_server, successful: true) }
    let(:api_key) { create(:mcp_server_api_key, purchase: purchase, link: mcp_server) }

    scenario 'Per-request pricing is calculated correctly' do
      mcp_server_config.update!(
        pricing_model: 'per_request',
        per_request_price_cents: 10,
        free_tier_requests: 100
      )

      # Simulate 150 API calls
      create(:mcp_server_usage,
             link: mcp_server,
             purchase: purchase,
             api_key_hash: api_key.key_hash,
             total_requests: 150,
             successful_requests: 150,
             usage_date: Date.current)

      cost = mcp_server_config.calculate_usage_cost(150)
      expect(cost).to eq(500) # 50 requests * 10 cents = 500 cents

      estimate = mcp_server_config.monthly_cost_estimate(150)
      expect(estimate[:usage_cost_cents]).to eq(500)
      expect(estimate[:total_cost_cents]).to eq(mcp_server.price_cents + 500)
    end
  end
end

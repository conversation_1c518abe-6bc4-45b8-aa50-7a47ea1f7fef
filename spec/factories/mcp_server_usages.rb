# frozen_string_literal: true

FactoryBot.define do
  factory :mcp_server_usage do
    association :link, factory: [:product, :mcp_server]
    association :purchase
    usage_date { Date.current }
    total_requests { 100 }
    successful_requests { 95 }
    failed_requests { 5 }
    total_response_time_ms { 5000 }
    api_key_hash { "test_api_key_hash" }

    trait :high_usage do
      total_requests { 1000 }
      successful_requests { 950 }
      failed_requests { 50 }
      total_response_time_ms { 50000 }
    end

    trait :low_usage do
      total_requests { 10 }
      successful_requests { 10 }
      failed_requests { 0 }
      total_response_time_ms { 500 }
    end

    trait :high_failure_rate do
      total_requests { 100 }
      successful_requests { 60 }
      failed_requests { 40 }
    end

    trait :yesterday do
      usage_date { Date.yesterday }
    end

    trait :last_week do
      usage_date { 1.week.ago.to_date }
    end

    trait :last_month do
      usage_date { 1.month.ago.to_date }
    end
  end
end

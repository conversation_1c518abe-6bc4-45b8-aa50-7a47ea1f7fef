# frozen_string_literal: true

FactoryBot.define do
  factory :mcp_server_config do
    association :link, factory: [:link, :mcp_server]
    endpoint_url { "https://example.com/api/mcp" }
    is_active { true }
    health_status { "healthy" }
    last_health_check_at { 5.minutes.ago }
    api_documentation { "This is a sample MCP server for testing purposes." }
    github_url { "https://github.com/example/mcp-server" }
    supported_tools { ["file_operations", "web_search"] }
    server_metadata { { "version" => "1.0.0", "author" => "Test Author" } }
    pricing_model { "fixed" }
    per_request_price_cents { 0 }
    free_tier_requests { 100 }
    pricing_tiers { [] }

    trait :inactive do
      is_active { false }
    end

    trait :unhealthy do
      health_status { "unhealthy" }
    end

    trait :unknown_health do
      health_status { "unknown" }
    end

    trait :per_request_pricing do
      pricing_model { "per_request" }
      per_request_price_cents { 10 }
      free_tier_requests { 100 }
    end

    trait :hybrid_pricing do
      pricing_model { "hybrid" }
      per_request_price_cents { 5 }
      monthly_request_limit { 1000 }
      free_tier_requests { 0 }
    end

    trait :with_pricing_tiers do
      pricing_model { "per_request" }
      pricing_tiers do
        [
          { "max_requests" => 1000, "price_cents_per_request" => 10, "name" => "Basic" },
          { "max_requests" => 10000, "price_cents_per_request" => 8, "name" => "Standard" },
          { "max_requests" => nil, "price_cents_per_request" => 5, "name" => "Premium" }
        ]
      end
    end

    trait :needs_health_check do
      last_health_check_at { 10.minutes.ago }
    end

    trait :never_checked do
      last_health_check_at { nil }
    end
  end
end

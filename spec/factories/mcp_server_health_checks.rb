# frozen_string_literal: true

FactoryBot.define do
  factory :mcp_server_health_check do
    association :mcp_server_config
    checked_at { Time.current }
    success { true }
    response_time_ms { 150.0 }
    response_body { '{"status": "ok"}' }
    error_message { nil }
    endpoint_url { mcp_server_config.endpoint_url }

    trait :failed do
      success { false }
      response_body { nil }
      error_message { "Connection timeout" }
      response_time_ms { 30000.0 }
    end

    trait :slow_response do
      response_time_ms { 5000.0 }
    end

    trait :fast_response do
      response_time_ms { 50.0 }
    end

    trait :recent do
      checked_at { 1.minute.ago }
    end

    trait :old do
      checked_at { 1.day.ago }
    end

    trait :with_detailed_response do
      response_body do
        {
          "status" => "healthy",
          "version" => "1.0.0",
          "uptime" => 86400,
          "memory_usage" => "45%",
          "cpu_usage" => "12%"
        }.to_json
      end
    end

    trait :with_error_details do
      success { false }
      response_body { nil }
      error_message { "HTTP 500: Internal Server Error - Database connection failed" }
    end
  end
end

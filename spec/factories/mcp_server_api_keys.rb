# frozen_string_literal: true

FactoryBot.define do
  factory :mcp_server_api_key do
    association :purchase, factory: [:purchase, :successful]
    association :link, factory: [:link, :mcp_server]
    name { "Test API Key" }
    api_key { "gumroad_mcp_#{SecureRandom.hex(32)}" }
    status { "active" }
    permissions { ["read", "execute"] }
    rate_limits do
      {
        "requests_per_hour" => 1000,
        "requests_per_day" => 10000,
        "requests_per_month" => 100000
      }
    end
    metadata { {} }
    expires_at { 1.year.from_now }
    last_used_at { nil }

    # Ensure the purchase and link are related
    after(:build) do |api_key|
      api_key.purchase.link = api_key.link if api_key.purchase && api_key.link
    end

    trait :suspended do
      status { "suspended" }
    end

    trait :revoked do
      status { "revoked" }
    end

    trait :expired do
      expires_at { 1.day.ago }
    end

    trait :recently_used do
      last_used_at { 1.hour.ago }
    end

    trait :read_only do
      permissions { ["read"] }
    end

    trait :admin_permissions do
      permissions { ["read", "execute", "admin"] }
    end

    trait :no_permissions do
      permissions { [] }
    end

    trait :high_rate_limits do
      rate_limits do
        {
          "requests_per_hour" => 5000,
          "requests_per_day" => 50000,
          "requests_per_month" => 500000
        }
      end
    end

    trait :low_rate_limits do
      rate_limits do
        {
          "requests_per_hour" => 100,
          "requests_per_day" => 1000,
          "requests_per_month" => 10000
        }
      end
    end

    trait :with_metadata do
      metadata do
        {
          "created_by" => "test_user",
          "purpose" => "testing",
          "environment" => "development"
        }
      end
    end
  end
end

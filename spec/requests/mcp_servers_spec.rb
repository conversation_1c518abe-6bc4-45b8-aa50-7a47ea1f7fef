# frozen_string_literal: true

require 'spec_helper'

RSpec.describe "MCP Servers", type: :request do
  let(:user) { create(:user) }
  let(:mcp_server) { create(:product, :mcp_server, user: user) }
  let!(:mcp_server_config) { create(:mcp_server_config, link: mcp_server) }

  before do
    sign_in user
  end

  describe "GET /mcp_servers" do
    it "returns a successful response" do
      get mcp_servers_path
      expect(response).to have_http_status(:success)
    end

    it "renders the MCP servers page" do
      get mcp_servers_path
      expect(response.body).to include("MCP Servers")
    end

    it "loads the correct React component" do
      get mcp_servers_path
      expect(response.body).to include('react_component "McpServersPage"')
    end
  end

  describe "GET /mcp_servers/paged" do
    it "returns JSON response" do
      get mcp_servers_paged_path, params: { page: 1 }
      expect(response).to have_http_status(:success)
      expect(response.content_type).to include('application/json')
    end

    it "returns paginated MCP servers data" do
      get mcp_servers_paged_path, params: { page: 1 }
      json_response = JSON.parse(response.body)

      expect(json_response).to have_key('entries')
      expect(json_response).to have_key('pagination')
      expect(json_response['entries']).to be_an(Array)
    end

    it "includes MCP server in the response" do
      get mcp_servers_paged_path, params: { page: 1 }
      json_response = JSON.parse(response.body)

      server_ids = json_response['entries'].map { |s| s['id'] }
      expect(server_ids).to include(mcp_server.unique_permalink)
    end

    it "supports search functionality" do
      other_server = create(:product, :mcp_server, user: user, name: "Different Server")
      create(:mcp_server_config, link: other_server)

      get mcp_servers_paged_path, params: { page: 1, query: mcp_server.name }
      json_response = JSON.parse(response.body)

      server_names = json_response['entries'].map { |s| s['name'] }
      expect(server_names).to include(mcp_server.name)
      expect(server_names).not_to include("Different Server")
    end

    it "supports sorting" do
      newer_server = create(:product, :mcp_server, user: user, name: "Newer Server", created_at: 1.day.from_now)
      create(:mcp_server_config, link: newer_server)

      get mcp_servers_paged_path, params: {
        page: 1,
        sort_key: 'created_at',
        sort_direction: 'desc'
      }

      json_response = JSON.parse(response.body)
      server_names = json_response['entries'].map { |s| s['name'] }
      expect(server_names.first).to eq("Newer Server")
    end
  end

  describe "authorization" do
    context "when user is not signed in" do
      before { sign_out user }

      it "redirects to login for index" do
        get mcp_servers_path
        expect(response).to redirect_to(new_user_session_path)
      end

      it "returns unauthorized for paged endpoint" do
        get mcp_servers_paged_path, params: { page: 1 }
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "when user doesn't have permission" do
      let(:other_user) { create(:user) }

      before do
        sign_out user
        sign_in other_user
        allow_any_instance_of(LinkPolicy).to receive(:index?).and_return(false)
      end

      it "raises authorization error" do
        expect {
          get mcp_servers_path
        }.to raise_error(Pundit::NotAuthorizedError)
      end
    end
  end

  describe "with multiple MCP servers" do
    let!(:servers) do
      5.times.map do |i|
        server = create(:product, :mcp_server, user: user, name: "Server #{i}")
        create(:mcp_server_config, link: server)
        server
      end
    end

    it "returns all user's MCP servers" do
      get mcp_servers_paged_path, params: { page: 1 }
      json_response = JSON.parse(response.body)

      expect(json_response['entries'].length).to eq(6) # 5 + original mcp_server
    end

    it "doesn't return other users' servers" do
      other_user = create(:user)
      other_server = create(:product, :mcp_server, user: other_user, name: "Other User Server")
      create(:mcp_server_config, link: other_server)

      get mcp_servers_paged_path, params: { page: 1 }
      json_response = JSON.parse(response.body)

      server_names = json_response['entries'].map { |s| s['name'] }
      expect(server_names).not_to include("Other User Server")
    end
  end

  describe "pagination" do
    before do
      stub_const("McpServersController::PER_PAGE", 2)
      3.times do |i|
        server = create(:product, :mcp_server, user: user, name: "Server #{i}")
        create(:mcp_server_config, link: server)
      end
    end

    it "paginates results correctly" do
      get mcp_servers_paged_path, params: { page: 1 }
      json_response = JSON.parse(response.body)

      expect(json_response['entries'].length).to eq(2)
      expect(json_response['pagination']['page']).to eq(1)
      expect(json_response['pagination']['pages']).to eq(2)
    end

    it "returns second page correctly" do
      get mcp_servers_paged_path, params: { page: 2 }
      json_response = JSON.parse(response.body)

      expect(json_response['entries'].length).to eq(2) # 1 original + 1 from the 3 created
      expect(json_response['pagination']['page']).to eq(2)
    end
  end
end

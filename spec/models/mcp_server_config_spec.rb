# frozen_string_literal: true

require 'rails_helper'

RSpec.describe McpServerConfig, type: :model do
  let(:user) { create(:user) }
  let(:link) { create(:link, user: user, native_type: Link::NATIVE_TYPE_MCP_SERVER) }
  let(:mcp_server_config) { create(:mcp_server_config, link: link) }

  describe 'associations' do
    it { should belong_to(:link) }
    it { should have_many(:mcp_server_usages) }
    it { should have_many(:mcp_server_health_checks) }
  end

  describe 'validations' do
    it { should validate_presence_of(:endpoint_url) }
    it { should validate_presence_of(:link_id) }
    it { should validate_uniqueness_of(:link_id) }
    it { should validate_inclusion_of(:health_status).in_array(%w[healthy unhealthy unknown]) }
    it { should validate_inclusion_of(:pricing_model).in_array(%w[fixed per_request hybrid]) }
    it { should validate_numericality_of(:per_request_price_cents).is_greater_than_or_equal_to(0) }
    it { should validate_numericality_of(:free_tier_requests).is_greater_than_or_equal_to(0) }

    it 'validates endpoint_url format' do
      config = build(:mcp_server_config, endpoint_url: 'invalid-url')
      expect(config).not_to be_valid
      expect(config.errors[:endpoint_url]).to include('is invalid')
    end

    it 'accepts valid HTTP URLs' do
      config = build(:mcp_server_config, endpoint_url: 'http://example.com/api')
      expect(config).to be_valid
    end

    it 'accepts valid HTTPS URLs' do
      config = build(:mcp_server_config, endpoint_url: 'https://example.com/api')
      expect(config).to be_valid
    end
  end

  describe 'scopes' do
    let!(:active_config) { create(:mcp_server_config, is_active: true) }
    let!(:inactive_config) { create(:mcp_server_config, is_active: false) }
    let!(:healthy_config) { create(:mcp_server_config, health_status: 'healthy') }
    let!(:unhealthy_config) { create(:mcp_server_config, health_status: 'unhealthy') }

    it 'filters active configs' do
      expect(McpServerConfig.active).to include(active_config)
      expect(McpServerConfig.active).not_to include(inactive_config)
    end

    it 'filters healthy configs' do
      expect(McpServerConfig.healthy).to include(healthy_config)
      expect(McpServerConfig.healthy).not_to include(unhealthy_config)
    end

    it 'filters unhealthy configs' do
      expect(McpServerConfig.unhealthy).to include(unhealthy_config)
      expect(McpServerConfig.unhealthy).not_to include(healthy_config)
    end
  end

  describe 'pricing model methods' do
    it 'identifies fixed pricing' do
      config = build(:mcp_server_config, pricing_model: 'fixed')
      expect(config.fixed_pricing?).to be true
      expect(config.per_request_pricing?).to be false
      expect(config.hybrid_pricing?).to be false
    end

    it 'identifies per-request pricing' do
      config = build(:mcp_server_config, pricing_model: 'per_request')
      expect(config.per_request_pricing?).to be true
      expect(config.fixed_pricing?).to be false
      expect(config.hybrid_pricing?).to be false
    end

    it 'identifies hybrid pricing' do
      config = build(:mcp_server_config, pricing_model: 'hybrid')
      expect(config.hybrid_pricing?).to be true
      expect(config.fixed_pricing?).to be false
      expect(config.per_request_pricing?).to be false
    end
  end

  describe '#calculate_usage_cost' do
    context 'with fixed pricing' do
      let(:config) { build(:mcp_server_config, pricing_model: 'fixed') }

      it 'returns zero cost' do
        expect(config.calculate_usage_cost(1000)).to eq(0)
      end
    end

    context 'with per-request pricing' do
      let(:config) do
        build(:mcp_server_config, 
              pricing_model: 'per_request',
              per_request_price_cents: 10,
              free_tier_requests: 100)
      end

      it 'returns zero for requests within free tier' do
        expect(config.calculate_usage_cost(50)).to eq(0)
      end

      it 'calculates cost for requests beyond free tier' do
        expect(config.calculate_usage_cost(150)).to eq(500) # 50 * 10 cents
      end
    end

    context 'with hybrid pricing' do
      let(:config) do
        build(:mcp_server_config,
              pricing_model: 'hybrid',
              per_request_price_cents: 5,
              monthly_request_limit: 1000)
      end

      it 'returns zero for requests within monthly limit' do
        expect(config.calculate_usage_cost(500)).to eq(0)
      end

      it 'calculates overage cost' do
        expect(config.calculate_usage_cost(1200)).to eq(1000) # 200 * 5 cents
      end
    end
  end

  describe '#monthly_cost_estimate' do
    let(:config) do
      build(:mcp_server_config,
            pricing_model: 'per_request',
            per_request_price_cents: 10,
            free_tier_requests: 100)
    end

    before do
      allow(config.link).to receive(:price_cents).and_return(2000) # $20 base cost
    end

    it 'returns cost breakdown' do
      estimate = config.monthly_cost_estimate(500)
      
      expect(estimate[:base_cost_cents]).to eq(2000)
      expect(estimate[:usage_cost_cents]).to eq(4000) # 400 * 10 cents
      expect(estimate[:total_cost_cents]).to eq(6000)
      expect(estimate[:estimated_requests]).to eq(500)
      expect(estimate[:pricing_model]).to eq('per_request')
    end
  end

  describe '#needs_health_check?' do
    it 'returns true when never checked' do
      config = build(:mcp_server_config, last_health_check_at: nil)
      expect(config.needs_health_check?).to be true
    end

    it 'returns true when last check was over 5 minutes ago' do
      config = build(:mcp_server_config, last_health_check_at: 10.minutes.ago)
      expect(config.needs_health_check?).to be true
    end

    it 'returns false when recently checked' do
      config = build(:mcp_server_config, last_health_check_at: 2.minutes.ago)
      expect(config.needs_health_check?).to be false
    end
  end

  describe '#uptime_percentage' do
    let(:config) { create(:mcp_server_config) }

    before do
      # Create some health check records
      create(:mcp_server_health_check, mcp_server_config: config, success: true, checked_at: 1.day.ago)
      create(:mcp_server_health_check, mcp_server_config: config, success: true, checked_at: 2.days.ago)
      create(:mcp_server_health_check, mcp_server_config: config, success: false, checked_at: 3.days.ago)
    end

    it 'calculates uptime percentage' do
      expect(config.uptime_percentage(days: 7)).to eq(66.67)
    end
  end

  describe '#consecutive_failures' do
    let(:config) { create(:mcp_server_config, health_status: 'unhealthy') }

    before do
      # Create consecutive failures
      create(:mcp_server_health_check, mcp_server_config: config, success: false, checked_at: 1.minute.ago)
      create(:mcp_server_health_check, mcp_server_config: config, success: false, checked_at: 2.minutes.ago)
      create(:mcp_server_health_check, mcp_server_config: config, success: true, checked_at: 3.minutes.ago)
    end

    it 'counts consecutive failures from most recent' do
      expect(config.consecutive_failures).to eq(2)
    end
  end

  describe '#should_alert_for_downtime?' do
    let(:config) { create(:mcp_server_config) }

    it 'returns true when consecutive failures >= 3' do
      allow(config).to receive(:consecutive_failures).and_return(3)
      expect(config.should_alert_for_downtime?).to be true
    end

    it 'returns false when consecutive failures < 3' do
      allow(config).to receive(:consecutive_failures).and_return(2)
      expect(config.should_alert_for_downtime?).to be false
    end
  end
end

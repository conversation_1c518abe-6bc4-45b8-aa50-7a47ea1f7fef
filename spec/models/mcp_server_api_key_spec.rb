# frozen_string_literal: true

require 'rails_helper'

RSpec.describe McpServerApi<PERSON>ey, type: :model do
  let(:user) { create(:user) }
  let(:link) { create(:link, user: user, native_type: Link::NATIVE_TYPE_MCP_SERVER) }
  let(:purchase) { create(:purchase, user: user, link: link) }
  let(:api_key) { create(:mcp_server_api_key, purchase: purchase, link: link) }

  describe 'associations' do
    it { should belong_to(:purchase) }
    it { should belong_to(:link) }
    it { should have_many(:mcp_server_usages) }
  end

  describe 'validations' do
    it { should validate_presence_of(:purchase_id) }
    it { should validate_presence_of(:link_id) }
    it { should validate_presence_of(:key_hash) }
    it { should validate_presence_of(:name) }
    it { should validate_uniqueness_of(:key_hash) }
    it { should validate_uniqueness_of(:purchase_id).scoped_to(:link_id) }
    it { should validate_length_of(:name).is_at_most(100) }
    it { should validate_inclusion_of(:status).in_array(%w[active suspended revoked]) }
  end

  describe 'scopes' do
    let!(:active_key) { create(:mcp_server_api_key, status: 'active') }
    let!(:suspended_key) { create(:mcp_server_api_key, status: 'suspended') }
    let!(:revoked_key) { create(:mcp_server_api_key, status: 'revoked') }

    it 'filters active keys' do
      expect(McpServerApiKey.active).to include(active_key)
      expect(McpServerApiKey.active).not_to include(suspended_key, revoked_key)
    end

    it 'filters suspended keys' do
      expect(McpServerApiKey.suspended).to include(suspended_key)
      expect(McpServerApiKey.suspended).not_to include(active_key, revoked_key)
    end

    it 'filters revoked keys' do
      expect(McpServerApiKey.revoked).to include(revoked_key)
      expect(McpServerApiKey.revoked).not_to include(active_key, suspended_key)
    end
  end

  describe 'callbacks' do
    it 'generates API key on creation' do
      api_key = build(:mcp_server_api_key, api_key: nil)
      api_key.save!
      expect(api_key.api_key).to be_present
      expect(api_key.api_key).to start_with('gumroad_mcp_')
    end

    it 'sets defaults on creation' do
      api_key = create(:mcp_server_api_key, 
                      status: nil, 
                      expires_at: nil, 
                      rate_limits: nil, 
                      permissions: nil)
      
      expect(api_key.status).to eq('active')
      expect(api_key.expires_at).to be_present
      expect(api_key.rate_limits).to be_present
      expect(api_key.permissions).to eq([])
    end

    it 'updates key hash when API key changes' do
      original_hash = api_key.key_hash
      api_key.update!(api_key: 'new_key')
      expect(api_key.key_hash).not_to eq(original_hash)
    end
  end

  describe '.authenticate' do
    let(:raw_api_key) { 'gumroad_mcp_test_key' }
    let!(:api_key_record) do
      create(:mcp_server_api_key, 
             api_key: raw_api_key,
             status: 'active',
             expires_at: 1.year.from_now)
    end

    it 'authenticates valid API key' do
      result = McpServerApiKey.authenticate(raw_api_key)
      expect(result).to eq(api_key_record)
    end

    it 'returns nil for invalid API key' do
      result = McpServerApiKey.authenticate('invalid_key')
      expect(result).to be_nil
    end

    it 'returns nil for expired API key' do
      api_key_record.update!(expires_at: 1.day.ago)
      result = McpServerApiKey.authenticate(raw_api_key)
      expect(result).to be_nil
    end

    it 'returns nil for inactive API key' do
      api_key_record.update!(status: 'suspended')
      result = McpServerApiKey.authenticate(raw_api_key)
      expect(result).to be_nil
    end

    it 'updates last_used_at on successful authentication' do
      expect {
        McpServerApiKey.authenticate(raw_api_key)
      }.to change { api_key_record.reload.last_used_at }
    end
  end

  describe '.generate_key' do
    it 'generates unique API keys' do
      key1 = McpServerApiKey.generate_key
      key2 = McpServerApiKey.generate_key
      
      expect(key1).not_to eq(key2)
      expect(key1).to start_with('gumroad_mcp_')
      expect(key2).to start_with('gumroad_mcp_')
    end
  end

  describe '.hash_api_key' do
    it 'generates consistent hashes' do
      key = 'test_key'
      hash1 = McpServerApiKey.hash_api_key(key)
      hash2 = McpServerApiKey.hash_api_key(key)
      
      expect(hash1).to eq(hash2)
      expect(hash1).to be_present
    end

    it 'generates different hashes for different keys' do
      hash1 = McpServerApiKey.hash_api_key('key1')
      hash2 = McpServerApiKey.hash_api_key('key2')
      
      expect(hash1).not_to eq(hash2)
    end
  end

  describe 'status methods' do
    it 'identifies active status' do
      api_key = build(:mcp_server_api_key, status: 'active')
      expect(api_key.active?).to be true
      expect(api_key.suspended?).to be false
      expect(api_key.revoked?).to be false
    end

    it 'identifies suspended status' do
      api_key = build(:mcp_server_api_key, status: 'suspended')
      expect(api_key.suspended?).to be true
      expect(api_key.active?).to be false
      expect(api_key.revoked?).to be false
    end

    it 'identifies revoked status' do
      api_key = build(:mcp_server_api_key, status: 'revoked')
      expect(api_key.revoked?).to be true
      expect(api_key.active?).to be false
      expect(api_key.suspended?).to be false
    end
  end

  describe '#expired?' do
    it 'returns false when no expiration date' do
      api_key = build(:mcp_server_api_key, expires_at: nil)
      expect(api_key.expired?).to be false
    end

    it 'returns false when not yet expired' do
      api_key = build(:mcp_server_api_key, expires_at: 1.day.from_now)
      expect(api_key.expired?).to be false
    end

    it 'returns true when expired' do
      api_key = build(:mcp_server_api_key, expires_at: 1.day.ago)
      expect(api_key.expired?).to be true
    end
  end

  describe 'status change methods' do
    it 'suspends API key' do
      api_key.suspend!
      expect(api_key.status).to eq('suspended')
    end

    it 'revokes API key' do
      api_key.revoke!
      expect(api_key.status).to eq('revoked')
    end

    it 'activates API key' do
      api_key.update!(status: 'suspended')
      api_key.activate!
      expect(api_key.status).to eq('active')
    end
  end

  describe '#rate_limit_exceeded?' do
    let(:api_key) do
      create(:mcp_server_api_key, 
             rate_limits: { "requests_per_hour" => 100 })
    end

    before do
      # Create usage records
      create(:mcp_server_usage, 
             api_key_hash: api_key.key_hash,
             api_calls_count: 50,
             created_at: 30.minutes.ago)
      create(:mcp_server_usage, 
             api_key_hash: api_key.key_hash,
             api_calls_count: 60,
             created_at: 10.minutes.ago)
    end

    it 'returns true when rate limit exceeded' do
      expect(api_key.rate_limit_exceeded?(limit: 100)).to be true
    end

    it 'returns false when within rate limit' do
      expect(api_key.rate_limit_exceeded?(limit: 200)).to be false
    end
  end

  describe '#has_permission?' do
    let(:api_key) { build(:mcp_server_api_key, permissions: ['read', 'execute']) }

    it 'returns true for granted permissions' do
      expect(api_key.has_permission?('read')).to be true
      expect(api_key.has_permission?(:execute)).to be true
    end

    it 'returns false for denied permissions' do
      expect(api_key.has_permission?('admin')).to be false
    end

    it 'returns true when no permissions set (no restrictions)' do
      api_key.permissions = []
      expect(api_key.has_permission?('anything')).to be true
    end
  end

  describe '#masked_key' do
    let(:api_key) { build(:mcp_server_api_key, api_key: 'gumroad_mcp_1234567890abcdef1234567890abcdef') }

    it 'masks the middle part of the API key' do
      masked = api_key.masked_key
      expect(masked).to start_with('gumroad_mcp_123')
      expect(masked).to end_with('bcdef')
      expect(masked).to include('*' * 20)
    end
  end

  describe '#usage_stats' do
    let(:api_key) { create(:mcp_server_api_key) }

    before do
      create(:mcp_server_usage,
             api_key_hash: api_key.key_hash,
             total_requests: 100,
             successful_requests: 95,
             failed_requests: 5,
             total_response_time_ms: 15000,
             usage_date: 1.day.ago)
    end

    it 'calculates usage statistics' do
      stats = api_key.usage_stats(days: 7)
      
      expect(stats[:total_requests]).to eq(100)
      expect(stats[:successful_requests]).to eq(95)
      expect(stats[:failed_requests]).to eq(5)
      expect(stats[:success_rate]).to eq(95.0)
    end
  end
end

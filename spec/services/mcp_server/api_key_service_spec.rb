# frozen_string_literal: true

require 'rails_helper'

RSpec.describe McpServer::ApiKeyService do
  let(:user) { create(:user) }
  let(:link) { create(:link, user: user, native_type: Link::NATIVE_TYPE_MCP_SERVER) }
  let(:purchase) { create(:purchase, user: user, link: link, successful: true) }

  describe '.create_for_purchase' do
    context 'with valid purchase' do
      it 'creates API key successfully' do
        result = described_class.create_for_purchase(
          purchase: purchase,
          link: link,
          name: 'Test API Key'
        )

        expect(result[:success]).to be true
        expect(result[:api_key]).to be_present
        expect(result[:api_key]).to start_with('gumroad_mcp_')
        expect(result[:masked_key]).to be_present
        expect(result[:api_key_id]).to be_present
      end

      it 'sets custom permissions and rate limits' do
        result = described_class.create_for_purchase(
          purchase: purchase,
          link: link,
          permissions: ['read', 'execute'],
          rate_limits: { 'requests_per_hour' => 500 }
        )

        api_key = McpServerApiKey.find(result[:api_key_id])
        expect(api_key.permissions).to eq(['read', 'execute'])
        expect(api_key.rate_limits['requests_per_hour']).to eq(500)
      end
    end

    context 'with existing API key' do
      before do
        create(:mcp_server_api_key, purchase: purchase, link: link)
      end

      it 'returns error for duplicate API key' do
        result = described_class.create_for_purchase(
          purchase: purchase,
          link: link
        )

        expect(result[:success]).to be false
        expect(result[:error]).to include('API key already exists')
      end
    end

    context 'with mismatched purchase and link' do
      let(:other_link) { create(:link, native_type: Link::NATIVE_TYPE_MCP_SERVER) }

      it 'returns error' do
        result = described_class.create_for_purchase(
          purchase: purchase,
          link: other_link
        )

        expect(result[:success]).to be false
        expect(result[:error]).to include('Purchase does not match')
      end
    end

    context 'with unsuccessful purchase' do
      let(:failed_purchase) { create(:purchase, user: user, link: link, successful: false) }

      it 'returns error' do
        result = described_class.create_for_purchase(
          purchase: failed_purchase,
          link: link
        )

        expect(result[:success]).to be false
        expect(result[:error]).to include('Purchase must be successful')
      end
    end
  end

  describe '.authenticate_and_authorize' do
    let(:api_key_record) { create(:mcp_server_api_key, purchase: purchase, link: link) }
    let(:service) { described_class.new(api_key_record.api_key) }

    context 'with valid API key' do
      it 'authenticates successfully' do
        result = service.authenticate_and_authorize(link: link)
        expect(result).to eq(api_key_record)
      end

      it 'checks permissions when specified' do
        api_key_record.update!(permissions: ['read'])
        
        expect {
          service.authenticate_and_authorize(link: link, permission: 'admin')
        }.to raise_error(McpServer::ApiKeyService::InsufficientPermissionsError)
      end

      it 'allows request with correct permission' do
        api_key_record.update!(permissions: ['read', 'execute'])
        
        result = service.authenticate_and_authorize(link: link, permission: 'read')
        expect(result).to eq(api_key_record)
      end
    end

    context 'with invalid API key' do
      let(:service) { described_class.new('invalid_key') }

      it 'raises authentication error' do
        expect {
          service.authenticate_and_authorize(link: link)
        }.to raise_error(McpServer::ApiKeyService::InvalidApiKeyError)
      end
    end

    context 'with wrong server' do
      let(:other_link) { create(:link, native_type: Link::NATIVE_TYPE_MCP_SERVER) }

      it 'raises authentication error' do
        expect {
          service.authenticate_and_authorize(link: other_link)
        }.to raise_error(McpServer::ApiKeyService::InvalidApiKeyError)
      end
    end

    context 'with rate limit exceeded' do
      before do
        allow(api_key_record).to receive(:rate_limit_exceeded?).and_return(true)
      end

      it 'raises rate limit error' do
        expect {
          service.authenticate_and_authorize(link: link)
        }.to raise_error(McpServer::ApiKeyService::RateLimitExceededError)
      end
    end
  end

  describe '#record_api_usage' do
    let(:api_key_record) { create(:mcp_server_api_key, purchase: purchase, link: link) }
    let(:service) { described_class.new(api_key_record.api_key) }

    before do
      service.instance_variable_set(:@api_key_record, api_key_record)
    end

    it 'records API usage' do
      expect(McpServerUsage).to receive(:record_api_call).with(
        link: link,
        purchase: purchase,
        api_key_hash: api_key_record.key_hash,
        response_time_ms: 150.0,
        success: true,
        metadata: { test: 'data' }
      )

      service.record_api_usage(
        response_time_ms: 150.0,
        success: true,
        metadata: { test: 'data' }
      )
    end
  end

  describe '#revoke_api_key' do
    let(:api_key_record) { create(:mcp_server_api_key, purchase: purchase, link: link) }
    let(:service) { described_class.new }

    context 'with authorized user' do
      it 'revokes API key successfully' do
        result = service.revoke_api_key(api_key_id: api_key_record.id, user: user)
        
        expect(result[:success]).to be true
        expect(api_key_record.reload.revoked?).to be true
      end
    end

    context 'with unauthorized user' do
      let(:other_user) { create(:user) }

      it 'returns error' do
        result = service.revoke_api_key(api_key_id: api_key_record.id, user: other_user)
        
        expect(result[:success]).to be false
        expect(result[:error]).to include('Unauthorized')
      end
    end

    context 'with server owner' do
      it 'allows server owner to revoke keys' do
        result = service.revoke_api_key(api_key_id: api_key_record.id, user: link.user)
        
        expect(result[:success]).to be true
        expect(api_key_record.reload.revoked?).to be true
      end
    end
  end

  describe '#list_api_keys_for_user' do
    let(:service) { described_class.new }
    let!(:api_key1) { create(:mcp_server_api_key, purchase: purchase, link: link) }
    let!(:api_key2) { create(:mcp_server_api_key, purchase: create(:purchase, user: user, link: create(:link, user: user, native_type: Link::NATIVE_TYPE_MCP_SERVER))) }

    it 'returns all API keys for user' do
      result = service.list_api_keys_for_user(user: user)
      
      expect(result.length).to eq(2)
      expect(result.map { |key| key['id'] }).to contain_exactly(api_key1.id, api_key2.id)
    end

    it 'filters by specific link' do
      result = service.list_api_keys_for_user(user: user, link: link)
      
      expect(result.length).to eq(1)
      expect(result.first['id']).to eq(api_key1.id)
    end

    it 'excludes API keys from other users' do
      other_user = create(:user)
      other_purchase = create(:purchase, user: other_user, link: link)
      create(:mcp_server_api_key, purchase: other_purchase, link: link)

      result = service.list_api_keys_for_user(user: user)
      
      expect(result.length).to eq(2) # Only user's keys
    end
  end
end

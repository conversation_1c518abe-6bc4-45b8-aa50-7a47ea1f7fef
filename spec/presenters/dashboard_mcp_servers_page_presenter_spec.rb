# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DashboardMcpServersPagePresenter do
  let(:user) { create(:user) }
  let(:pundit_user) { PunditUser.new(user: user, seller: user) }
  let(:mcp_server) { create(:link, :mcp_server, user: user, name: "Test MCP Server", price_cents: 1000) }
  let!(:mcp_server_config) { create(:mcp_server_config, link: mcp_server, health_status: "healthy") }
  let(:pagination) { { page: 1, pages: 1 } }

  subject(:presenter) do
    described_class.new(
      pundit_user: pundit_user,
      mcp_servers: [mcp_server],
      mcp_servers_pagination: pagination
    )
  end

  describe "#page_props" do
    let(:page_props) { presenter.page_props }

    it "returns the correct structure" do
      expect(page_props).to have_key(:mcp_servers)
      expect(page_props).to have_key(:mcp_servers_pagination)
      expect(page_props).to have_key(:can_create_mcp_server)
    end

    it "includes MCP server data" do
      mcp_servers = page_props[:mcp_servers]
      expect(mcp_servers).to be_an(Array)
      expect(mcp_servers.length).to eq(1)
      
      server_data = mcp_servers.first
      expect(server_data["id"]).to eq(mcp_server.unique_permalink)
      expect(server_data["name"]).to eq("Test MCP Server")
      expect(server_data["price_formatted"]).to be_present
      expect(server_data["health_status"]).to eq("healthy")
      expect(server_data["health_status_display"]).to eq("Healthy")
    end

    it "includes authorization flags" do
      server_data = page_props[:mcp_servers].first
      expect(server_data).to have_key("can_edit")
      expect(server_data).to have_key("can_destroy")
      expect(server_data).to have_key("can_duplicate")
    end

    it "includes usage statistics" do
      server_data = page_props[:mcp_servers].first
      expect(server_data).to have_key("total_requests")
      expect(server_data).to have_key("successful_requests")
      expect(server_data).to have_key("failed_requests")
      expect(server_data).to have_key("success_rate")
      expect(server_data).to have_key("average_response_time_ms")
    end

    it "includes URLs and metadata" do
      server_data = page_props[:mcp_servers].first
      expect(server_data).to have_key("url")
      expect(server_data).to have_key("edit_url")
      expect(server_data).to have_key("endpoint_url")
      expect(server_data).to have_key("published")
      expect(server_data).to have_key("status")
    end
  end

  describe "#table_props" do
    let(:table_props) { presenter.table_props }

    it "returns the correct structure for table component" do
      expect(table_props).to have_key(:mcp_servers)
      expect(table_props).to have_key(:mcp_servers_pagination)
      expect(table_props).not_to have_key(:can_create_mcp_server)
    end
  end

  describe "health status formatting" do
    it "formats healthy status correctly" do
      mcp_server_config.update!(health_status: "healthy")
      server_data = presenter.page_props[:mcp_servers].first
      expect(server_data["health_status_display"]).to eq("Healthy")
    end

    it "formats unhealthy status correctly" do
      mcp_server_config.update!(health_status: "unhealthy")
      server_data = presenter.page_props[:mcp_servers].first
      expect(server_data["health_status_display"]).to eq("Unhealthy")
    end

    it "formats unknown status correctly" do
      mcp_server_config.update!(health_status: "unknown")
      server_data = presenter.page_props[:mcp_servers].first
      expect(server_data["health_status_display"]).to eq("Unknown")
    end
  end

  describe "price formatting" do
    it "formats price correctly" do
      server_data = presenter.page_props[:mcp_servers].first
      expect(server_data["price_formatted"]).to eq("$10")
      expect(server_data["display_price_cents"]).to eq(1000)
    end
  end

  describe "usage statistics calculation" do
    before do
      # Create some usage data
      create(:mcp_server_usage, 
        link: mcp_server,
        usage_date: Date.current,
        total_requests: 100,
        successful_requests: 95,
        failed_requests: 5,
        total_response_time_ms: 5000
      )
    end

    it "calculates usage statistics correctly" do
      server_data = presenter.page_props[:mcp_servers].first
      expect(server_data["total_requests"]).to eq(100)
      expect(server_data["successful_requests"]).to eq(95)
      expect(server_data["failed_requests"]).to eq(5)
      expect(server_data["success_rate"]).to eq(95.0)
      expect(server_data["average_response_time_ms"]).to eq(50.0)
    end
  end

  describe "authorization" do
    context "when user has full permissions" do
      it "sets all permission flags to true" do
        server_data = presenter.page_props[:mcp_servers].first
        expect(server_data["can_edit"]).to be true
        expect(server_data["can_destroy"]).to be true
        expect(server_data["can_duplicate"]).to be true
      end
    end

    context "when user has limited permissions" do
      let(:other_user) { create(:user) }
      let(:other_pundit_user) { PunditUser.new(user: other_user, seller: other_user) }
      
      subject(:presenter) do
        described_class.new(
          pundit_user: other_pundit_user,
          mcp_servers: [mcp_server],
          mcp_servers_pagination: pagination
        )
      end

      it "sets permission flags based on policy" do
        server_data = presenter.page_props[:mcp_servers].first
        expect(server_data["can_edit"]).to be false
        expect(server_data["can_destroy"]).to be false
        expect(server_data["can_duplicate"]).to be true # Users can generally create new servers
      end
    end
  end
end

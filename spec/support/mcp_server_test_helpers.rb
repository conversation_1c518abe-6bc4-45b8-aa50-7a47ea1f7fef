# frozen_string_literal: true

module McpServerTestHelpers
  # Helper methods for testing MCP server functionality

  def create_mcp_server_with_config(user: nil, **config_attributes)
    user ||= create(:user)
    link = create(:link, user: user, native_type: Link::NATIVE_TYPE_MCP_SERVER)
    config = create(:mcp_server_config, link: link, **config_attributes)
    [link, config]
  end

  def create_api_key_for_server(link, user: nil, **api_key_attributes)
    user ||= create(:user)
    purchase = create(:purchase, user: user, link: link, successful: true)
    create(:mcp_server_api_key, purchase: purchase, link: link, **api_key_attributes)
  end

  def mock_mcp_server_response(endpoint_url, path: '', method: :get, status: 200, body: '{"status": "ok"}', headers: {})
    full_url = "#{endpoint_url.chomp('/')}/#{path.sub(/^\//, '')}"
    default_headers = { 'Content-Type' => 'application/json' }
    
    stub_request(method, full_url)
      .to_return(
        status: status,
        body: body,
        headers: default_headers.merge(headers)
      )
  end

  def mock_health_check_response(config, success: true, response_time: 150)
    if success
      mock_mcp_server_response(
        config.endpoint_url,
        path: '/health',
        status: 200,
        body: '{"status": "healthy", "uptime": 86400}'
      )
    else
      stub_request(:get, "#{config.endpoint_url}/health")
        .to_timeout
    end
  end

  def simulate_api_usage(api_key, requests: 10, success_rate: 1.0, avg_response_time: 150)
    successful_requests = (requests * success_rate).round
    failed_requests = requests - successful_requests
    
    create(:mcp_server_usage,
           link: api_key.link,
           purchase: api_key.purchase,
           api_key_hash: api_key.key_hash,
           total_requests: requests,
           successful_requests: successful_requests,
           failed_requests: failed_requests,
           average_response_time_ms: avg_response_time,
           total_response_time_ms: requests * avg_response_time,
           usage_date: Date.current)
  end

  def simulate_health_checks(config, count: 5, success_rate: 1.0)
    count.times do |i|
      success = i < (count * success_rate).round
      create(:mcp_server_health_check,
             mcp_server_config: config,
             success: success,
             response_time_ms: success ? rand(50..300) : rand(5000..30000),
             checked_at: i.hours.ago,
             error_message: success ? nil : 'Connection timeout')
    end
  end

  def expect_api_gateway_request(server_id, path: '', method: :post, api_key: nil)
    gateway_path = "/api/mcp/#{server_id}"
    gateway_path += "/#{path}" if path.present?
    
    headers = {}
    headers['Authorization'] = "Bearer #{api_key}" if api_key
    
    expect(self).to receive(method).with(gateway_path, anything).and_call_original if respond_to?(method)
  end

  def assert_rate_limit_headers(response, limit: nil, remaining: nil)
    expect(response.headers['X-RateLimit-Limit']).to be_present
    expect(response.headers['X-RateLimit-Remaining']).to be_present
    expect(response.headers['X-RateLimit-Reset']).to be_present
    
    if limit
      expect(response.headers['X-RateLimit-Limit'].to_i).to eq(limit)
    end
    
    if remaining
      expect(response.headers['X-RateLimit-Remaining'].to_i).to eq(remaining)
    end
  end

  def assert_usage_recorded(api_key, expected_requests: 1, success: true)
    usage = McpServerUsage.where(api_key_hash: api_key.key_hash).last
    expect(usage).to be_present
    expect(usage.total_requests).to eq(expected_requests)
    
    if success
      expect(usage.successful_requests).to eq(expected_requests)
      expect(usage.failed_requests).to eq(0)
    else
      expect(usage.failed_requests).to eq(expected_requests)
      expect(usage.successful_requests).to eq(0)
    end
  end

  def with_redis_cleanup
    keys_before = Redis.current.keys("mcp_rate_limit:*")
    yield
  ensure
    # Clean up any rate limit keys created during the test
    keys_after = Redis.current.keys("mcp_rate_limit:*")
    new_keys = keys_after - keys_before
    Redis.current.del(*new_keys) if new_keys.any?
  end

  def travel_to_reset_rate_limits
    # Travel to a time that would reset all rate limits
    travel_to(2.hours.from_now) do
      yield
    end
  end

  # Matcher for testing pricing calculations
  RSpec::Matchers.define :have_pricing_estimate do |expected|
    match do |config|
      estimate = config.monthly_cost_estimate(expected[:requests])
      
      @actual_base = estimate[:base_cost_cents]
      @actual_usage = estimate[:usage_cost_cents]
      @actual_total = estimate[:total_cost_cents]
      
      @expected_base = expected[:base_cost_cents]
      @expected_usage = expected[:usage_cost_cents]
      @expected_total = expected[:total_cost_cents]
      
      @actual_base == @expected_base &&
        @actual_usage == @expected_usage &&
        @actual_total == @expected_total
    end

    failure_message do |config|
      "Expected pricing estimate to be base: #{@expected_base}, usage: #{@expected_usage}, total: #{@expected_total}, " \
      "but got base: #{@actual_base}, usage: #{@actual_usage}, total: #{@actual_total}"
    end
  end
end

RSpec.configure do |config|
  config.include McpServerTestHelpers, type: :model
  config.include McpServerTestHelpers, type: :controller
  config.include McpServerTestHelpers, type: :feature
  config.include McpServerTestHelpers, type: :service
end

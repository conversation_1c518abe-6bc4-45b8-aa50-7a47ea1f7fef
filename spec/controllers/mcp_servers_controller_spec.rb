# frozen_string_literal: true

require 'rails_helper'

RSpec.describe McpServersController, type: :controller do
  let(:user) { create(:user) }
  let(:mcp_server) { create(:link, :mcp_server, user: user) }
  let!(:mcp_server_config) { create(:mcp_server_config, link: mcp_server) }

  before do
    sign_in user
  end

  describe "GET #index" do
    it "returns a successful response" do
      get :index
      expect(response).to be_successful
    end

    it "assigns the correct instance variables" do
      get :index
      expect(assigns(:react_mcp_servers_page_props)).to be_present
      expect(assigns(:react_mcp_servers_page_props)[:mcp_servers]).to be_an(Array)
      expect(assigns(:react_mcp_servers_page_props)[:mcp_servers_pagination]).to be_present
      expect(assigns(:react_mcp_servers_page_props)[:can_create_mcp_server]).to be_in([true, false])
    end

    it "includes MCP servers in the response" do
      get :index
      mcp_servers = assigns(:react_mcp_servers_page_props)[:mcp_servers]
      expect(mcp_servers).to include(
        hash_including(
          "id" => mcp_server.unique_permalink,
          "name" => mcp_server.name,
          "description" => mcp_server.description
        )
      )
    end

    context "when user cannot create MCP servers" do
      before do
        allow_any_instance_of(LinkPolicy).to receive(:create?).and_return(false)
      end

      it "sets can_create_mcp_server to false" do
        get :index
        expect(assigns(:react_mcp_servers_page_props)[:can_create_mcp_server]).to be false
      end
    end
  end

  describe "GET #mcp_servers_paged" do
    it "returns JSON response" do
      get :mcp_servers_paged, params: { page: 1 }, format: :json
      expect(response).to be_successful
      expect(response.content_type).to include('application/json')
    end

    it "returns paginated MCP servers" do
      get :mcp_servers_paged, params: { page: 1 }, format: :json
      json_response = JSON.parse(response.body)
      
      expect(json_response).to have_key('entries')
      expect(json_response).to have_key('pagination')
      expect(json_response['entries']).to be_an(Array)
    end

    it "filters by query parameter" do
      other_server = create(:link, :mcp_server, user: user, name: "Different Server")
      create(:mcp_server_config, link: other_server)

      get :mcp_servers_paged, params: { page: 1, query: mcp_server.name }, format: :json
      json_response = JSON.parse(response.body)
      
      server_names = json_response['entries'].map { |s| s['name'] }
      expect(server_names).to include(mcp_server.name)
      expect(server_names).not_to include("Different Server")
    end

    it "sorts by specified parameters" do
      newer_server = create(:link, :mcp_server, user: user, name: "Newer Server", created_at: 1.day.from_now)
      create(:mcp_server_config, link: newer_server)

      get :mcp_servers_paged, params: { 
        page: 1, 
        sort_key: 'created_at', 
        sort_direction: 'desc' 
      }, format: :json
      
      json_response = JSON.parse(response.body)
      server_names = json_response['entries'].map { |s| s['name'] }
      expect(server_names.first).to eq("Newer Server")
    end
  end

  describe "authorization" do
    context "when user is not authorized" do
      before do
        allow_any_instance_of(LinkPolicy).to receive(:index?).and_return(false)
      end

      it "raises Pundit::NotAuthorizedError for index" do
        expect {
          get :index
        }.to raise_error(Pundit::NotAuthorizedError)
      end

      it "raises Pundit::NotAuthorizedError for mcp_servers_paged" do
        expect {
          get :mcp_servers_paged, params: { page: 1 }, format: :json
        }.to raise_error(Pundit::NotAuthorizedError)
      end
    end
  end
end

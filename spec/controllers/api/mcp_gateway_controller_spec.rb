# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::McpGatewayController, type: :controller do
  let(:user) { create(:user) }
  let(:link) { create(:link, user: user, native_type: Link::NATIVE_TYPE_MCP_SERVER) }
  let(:mcp_server_config) { create(:mcp_server_config, link: link, is_active: true, health_status: 'healthy') }
  let(:purchase) { create(:purchase, user: user, link: link, successful: true) }
  let(:api_key_record) { create(:mcp_server_api_key, purchase: purchase, link: link, status: 'active') }
  let(:valid_api_key) { api_key_record.api_key }

  before do
    # Ensure the MCP server config exists
    mcp_server_config
  end

  describe 'GET #status' do
    context 'with valid API key' do
      before do
        request.headers['Authorization'] = "Bearer #{valid_api_key}"
      end

      it 'returns server status' do
        get :status, params: { server_id: link.external_id }
        
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        
        expect(json_response['server']['server_id']).to eq(link.external_id)
        expect(json_response['server']['health_status']).to eq('healthy')
        expect(json_response['api_key']['id']).to eq(api_key_record.id)
        expect(json_response['rate_limits']).to be_an(Array)
      end
    end

    context 'with invalid API key' do
      before do
        request.headers['Authorization'] = 'Bearer invalid_key'
      end

      it 'returns unauthorized' do
        get :status, params: { server_id: link.external_id }
        
        expect(response).to have_http_status(:unauthorized)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Invalid API key')
      end
    end

    context 'with missing API key' do
      it 'returns unauthorized' do
        get :status, params: { server_id: link.external_id }
        
        expect(response).to have_http_status(:unauthorized)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Missing API key')
      end
    end

    context 'with non-existent server' do
      before do
        request.headers['Authorization'] = "Bearer #{valid_api_key}"
      end

      it 'returns not found' do
        get :status, params: { server_id: 'non_existent' }
        
        expect(response).to have_http_status(:not_found)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('MCP server not found')
      end
    end
  end

  describe 'POST #proxy' do
    let(:mock_response) { double('response', success?: true, code: 200, body: '{"result": "success"}') }

    before do
      request.headers['Authorization'] = "Bearer #{valid_api_key}"
      allow_any_instance_of(McpServer::ProxyService).to receive(:proxy_request).and_return({
        success: true,
        status: 200,
        headers: { 'Content-Type' => 'application/json' },
        body: '{"result": "success"}'
      })
    end

    it 'proxies request successfully' do
      post :proxy, params: { server_id: link.external_id, path: 'test' }
      
      expect(response).to have_http_status(:ok)
      expect(response.body).to eq('{"result": "success"}')
      expect(response.headers['Content-Type']).to eq('application/json')
    end

    it 'adds rate limit headers' do
      post :proxy, params: { server_id: link.external_id, path: 'test' }
      
      expect(response.headers['X-RateLimit-Limit']).to be_present
      expect(response.headers['X-RateLimit-Remaining']).to be_present
      expect(response.headers['X-RateLimit-Reset']).to be_present
    end

    context 'with inactive server' do
      before do
        mcp_server_config.update!(is_active: false)
      end

      it 'returns service unavailable' do
        post :proxy, params: { server_id: link.external_id, path: 'test' }
        
        expect(response).to have_http_status(:service_unavailable)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Server unavailable')
      end
    end

    context 'with unhealthy server' do
      before do
        mcp_server_config.update!(health_status: 'unhealthy')
      end

      it 'returns service unavailable' do
        post :proxy, params: { server_id: link.external_id, path: 'test' }
        
        expect(response).to have_http_status(:service_unavailable)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Server unhealthy')
      end
    end

    context 'with rate limit exceeded' do
      before do
        allow_any_instance_of(McpServer::RateLimiterService).to receive(:check_rate_limit).and_return({
          allowed: false,
          limit: 1000,
          current: 1000,
          reset_at: 1.hour.from_now,
          retry_after: 3600
        })
      end

      it 'returns too many requests' do
        post :proxy, params: { server_id: link.external_id, path: 'test' }
        
        expect(response).to have_http_status(:too_many_requests)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Rate limit exceeded')
        expect(response.headers['Retry-After']).to be_present
      end
    end

    context 'with proxy service error' do
      before do
        allow_any_instance_of(McpServer::ProxyService).to receive(:proxy_request)
          .and_raise(McpServer::ProxyService::TimeoutError, 'Request timeout')
      end

      it 'returns gateway timeout' do
        post :proxy, params: { server_id: link.external_id, path: 'test' }
        
        expect(response).to have_http_status(:gateway_timeout)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Request timeout')
      end
    end
  end

  describe 'authentication' do
    context 'with Authorization header' do
      before do
        request.headers['Authorization'] = "Bearer #{valid_api_key}"
      end

      it 'authenticates successfully' do
        get :status, params: { server_id: link.external_id }
        expect(response).to have_http_status(:ok)
      end
    end

    context 'with X-API-Key header' do
      before do
        request.headers['X-API-Key'] = valid_api_key
      end

      it 'authenticates successfully' do
        get :status, params: { server_id: link.external_id }
        expect(response).to have_http_status(:ok)
      end
    end

    context 'with query parameter' do
      it 'authenticates successfully' do
        get :status, params: { server_id: link.external_id, api_key: valid_api_key }
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'permission checking' do
    let(:limited_api_key) do
      create(:mcp_server_api_key, 
             purchase: purchase, 
             link: link, 
             permissions: ['read'])
    end

    context 'with read permission for GET request' do
      before do
        request.headers['Authorization'] = "Bearer #{limited_api_key.api_key}"
      end

      it 'allows GET requests' do
        get :status, params: { server_id: link.external_id }
        expect(response).to have_http_status(:ok)
      end
    end

    context 'without execute permission for POST request' do
      before do
        request.headers['Authorization'] = "Bearer #{limited_api_key.api_key}"
        allow_any_instance_of(McpServer::ProxyService).to receive(:proxy_request).and_return({
          success: true,
          status: 200,
          headers: {},
          body: 'success'
        })
      end

      it 'denies POST requests' do
        post :proxy, params: { server_id: link.external_id, path: 'test' }
        expect(response).to have_http_status(:forbidden)
      end
    end
  end

  describe 'error handling' do
    before do
      request.headers['Authorization'] = "Bearer #{valid_api_key}"
    end

    it 'handles server unavailable errors' do
      allow_any_instance_of(McpServer::ProxyService).to receive(:proxy_request)
        .and_raise(McpServer::ProxyService::ServerUnavailableError, 'Server down')

      post :proxy, params: { server_id: link.external_id, path: 'test' }
      
      expect(response).to have_http_status(:service_unavailable)
      json_response = JSON.parse(response.body)
      expect(json_response['error']).to eq('Server unavailable')
    end

    it 'handles timeout errors' do
      allow_any_instance_of(McpServer::ProxyService).to receive(:proxy_request)
        .and_raise(McpServer::ProxyService::TimeoutError, 'Timeout')

      post :proxy, params: { server_id: link.external_id, path: 'test' }
      
      expect(response).to have_http_status(:gateway_timeout)
    end

    it 'handles general errors' do
      allow_any_instance_of(McpServer::ProxyService).to receive(:proxy_request)
        .and_raise(StandardError, 'Unexpected error')

      post :proxy, params: { server_id: link.external_id, path: 'test' }
      
      expect(response).to have_http_status(:internal_server_error)
      json_response = JSON.parse(response.body)
      expect(json_response['error']).to eq('Internal server error')
    end
  end
end

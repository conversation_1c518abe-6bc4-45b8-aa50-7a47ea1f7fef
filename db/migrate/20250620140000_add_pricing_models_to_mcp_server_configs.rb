# frozen_string_literal: true

class AddPricingModelsToMcpServerConfigs < ActiveRecord::Migration[7.1]
  # Disable pt-online-schema-change for this migration
  disable_ddl_transaction!

  def up
    # Add columns one by one to avoid foreign key issues
    execute "ALTER TABLE mcp_server_configs ADD COLUMN pricing_model VARCHAR(255) DEFAULT 'fixed' NOT NULL"
    execute "ALTER TABLE mcp_server_configs ADD COLUMN per_request_price_cents INT DEFAULT 0 NOT NULL"
    execute "ALTER TABLE mcp_server_configs ADD COLUMN monthly_request_limit INT"
    execute "ALTER TABLE mcp_server_configs ADD COLUMN free_tier_requests INT DEFAULT 0 NOT NULL"
    execute "ALTER TABLE mcp_server_configs ADD COLUMN pricing_tiers JSON"

    add_index :mcp_server_configs, :pricing_model
  end

  def down
    remove_index :mcp_server_configs, :pricing_model
    remove_column :mcp_server_configs, :pricing_tiers
    remove_column :mcp_server_configs, :free_tier_requests
    remove_column :mcp_server_configs, :monthly_request_limit
    remove_column :mcp_server_configs, :per_request_price_cents
    remove_column :mcp_server_configs, :pricing_model
  end
end

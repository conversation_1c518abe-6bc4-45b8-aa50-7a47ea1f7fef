# frozen_string_literal: true

class AddPerformanceIndexesToMcpTables < ActiveRecord::Migration[7.1]
  def change
    # Indexes for MCP server configs (only add if they don't exist)
    add_index :mcp_server_configs, [:is_active, :health_status], name: "index_mcp_configs_on_active_and_health" unless index_exists?(:mcp_server_configs, [:is_active, :health_status], name: "index_mcp_configs_on_active_and_health")
    add_index :mcp_server_configs, [:pricing_model], name: "index_mcp_configs_on_pricing_model" unless index_exists?(:mcp_server_configs, [:pricing_model], name: "index_mcp_configs_on_pricing_model")
    add_index :mcp_server_configs, [:last_health_check_at], name: "index_mcp_configs_on_last_health_check" unless index_exists?(:mcp_server_configs, [:last_health_check_at], name: "index_mcp_configs_on_last_health_check")

    # Indexes for MCP server usages (only add if they don't exist)
    add_index :mcp_server_usages, [:link_id, :usage_date], name: "index_mcp_usages_on_link_and_date" unless index_exists?(:mcp_server_usages, [:link_id, :usage_date], name: "index_mcp_usages_on_link_and_date")
    add_index :mcp_server_usages, [:purchase_id, :usage_date], name: "index_mcp_usages_on_purchase_and_date" unless index_exists?(:mcp_server_usages, [:purchase_id, :usage_date], name: "index_mcp_usages_on_purchase_and_date")
    add_index :mcp_server_usages, [:api_key_hash, :usage_date], name: "index_mcp_usages_on_api_key_and_date" unless index_exists?(:mcp_server_usages, [:api_key_hash, :usage_date], name: "index_mcp_usages_on_api_key_and_date")
    add_index :mcp_server_usages, [:created_at], name: "index_mcp_usages_on_created_at" unless index_exists?(:mcp_server_usages, [:created_at], name: "index_mcp_usages_on_created_at")

    # Indexes for API keys (only add if they don't exist)
    add_index :mcp_server_api_keys, [:link_id, :status], name: "index_mcp_api_keys_on_link_and_status" unless index_exists?(:mcp_server_api_keys, [:link_id, :status], name: "index_mcp_api_keys_on_link_and_status")
    add_index :mcp_server_api_keys, [:purchase_id, :status], name: "index_mcp_api_keys_on_purchase_and_status" unless index_exists?(:mcp_server_api_keys, [:purchase_id, :status], name: "index_mcp_api_keys_on_purchase_and_status")

    # Composite index for links with MCP server type (only add if it doesn't exist)
    unless index_exists?(:links, [:native_type, :draft], name: "index_links_on_native_type_and_draft")
      add_index :links, [:native_type, :draft],
                name: "index_links_on_native_type_and_draft",
                where: "native_type = 'mcp_server'"
    end
  end
end

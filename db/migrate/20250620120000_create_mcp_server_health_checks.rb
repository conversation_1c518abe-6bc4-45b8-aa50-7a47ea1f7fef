# frozen_string_literal: true

class CreateMcpServerHealthChecks < ActiveRecord::Migration[7.1]
  def change
    create_table :mcp_server_health_checks, charset: "utf8mb4", collation: "utf8mb4_unicode_ci" do |t|
      t.references :mcp_server_config, null: false, foreign_key: true
      t.datetime :checked_at, null: false
      t.boolean :success, null: false, default: false
      t.decimal :response_time_ms, precision: 10, scale: 2, null: false, default: 0.0
      t.text :response_body
      t.text :error_message
      t.string :endpoint_url, null: false
      t.timestamps

      t.index [:mcp_server_config_id, :checked_at], name: "index_health_checks_on_config_and_time"
      t.index [:checked_at], name: "index_health_checks_on_checked_at"
      t.index [:success, :checked_at], name: "index_health_checks_on_success_and_time"
      t.index [:mcp_server_config_id, :success], name: "index_health_checks_on_config_and_success"
    end
  end
end

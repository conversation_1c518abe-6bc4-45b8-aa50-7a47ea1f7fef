# frozen_string_literal: true

class CreateMcpServerApiKeys < ActiveRecord::Migration[7.1]
  def change
    create_table :mcp_server_api_keys, charset: "utf8mb4", collation: "utf8mb4_unicode_ci" do |t|
      t.references :purchase, null: false, foreign_key: true
      t.references :link, null: false, foreign_key: true
      t.string :name, null: false
      t.text :api_key, null: false # Encrypted in production
      t.string :key_hash, null: false # SHA-256 hash for lookup
      t.string :status, null: false, default: "active"
      t.json :permissions
      t.json :rate_limits
      t.json :metadata
      t.datetime :expires_at
      t.datetime :last_used_at
      t.timestamps

      t.index [:purchase_id, :link_id], unique: true, name: "index_api_keys_on_purchase_and_link"
      t.index [:key_hash], unique: true, name: "index_api_keys_on_key_hash"
      t.index [:status], name: "index_api_keys_on_status"
      t.index [:expires_at], name: "index_api_keys_on_expires_at"
      t.index [:last_used_at], name: "index_api_keys_on_last_used_at"
      t.index [:link_id, :status], name: "index_api_keys_on_link_and_status"
    end
  end
end

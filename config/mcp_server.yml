# MCP Server Configuration

development:
  # Gateway settings
  gateway:
    timeout: 30 # seconds
    open_timeout: 10 # seconds
    max_redirects: 3
    user_agent: "Gumroad-MCP-Gateway/1.0"

  # Health check settings
  health_check:
    interval: 60 # 1 minute for faster development
    timeout: 10 # seconds
    retry_attempts: 3
    failure_threshold: 3 # consecutive failures before marking unhealthy
    success_threshold: 2 # consecutive successes before marking healthy

  # Rate limiting
  rate_limiting:
    global:
      unauthenticated_requests_per_minute: 100
      authenticated_requests_per_minute: 1000
    default_limits:
      requests_per_hour: 1000
      requests_per_day: 10000
      requests_per_month: 100000
      burst_limit: 50
      concurrent_requests: 10

  # Pricing
  pricing:
    default_free_tier: 100
    minimum_per_request_price_cents: 1
    maximum_per_request_price_cents: 10000

  # Usage tracking
  usage_tracking:
    enabled: true
    batch_size: 100
    flush_interval: 60 # seconds
    retention_days: 90

  # Monitoring
  monitoring:
    enabled: true
    metrics_retention_days: 30
    alert_thresholds:
      error_rate_percent: 5.0
      response_time_ms: 5000
      uptime_percent: 95.0

  # Security
  security:
    api_key_length: 64
    api_key_prefix: "gumroad_mcp_"
    max_request_size_mb: 10
    allowed_content_types:
      - "application/json"
      - "text/plain"
      - "application/x-www-form-urlencoded"
      - "multipart/form-data"

test:
  # Gateway settings
  gateway:
    timeout: 30 # seconds
    open_timeout: 10 # seconds
    max_redirects: 3
    user_agent: "Gumroad-MCP-Gateway/1.0"

  # Health check settings
  health_check:
    interval: 10 # 10 seconds for tests
    timeout: 10 # seconds
    retry_attempts: 3
    failure_threshold: 3 # consecutive failures before marking unhealthy
    success_threshold: 2 # consecutive successes before marking healthy

  # Rate limiting
  rate_limiting:
    global:
      unauthenticated_requests_per_minute: 1000
      authenticated_requests_per_minute: 10000
    default_limits:
      requests_per_hour: 1000
      requests_per_day: 10000
      requests_per_month: 100000
      burst_limit: 50
      concurrent_requests: 10

  # Pricing
  pricing:
    default_free_tier: 100
    minimum_per_request_price_cents: 1
    maximum_per_request_price_cents: 10000

  # Usage tracking
  usage_tracking:
    enabled: false # Disable for tests
    batch_size: 100
    flush_interval: 60 # seconds
    retention_days: 90

  # Monitoring
  monitoring:
    enabled: false # Disable for tests
    metrics_retention_days: 30
    alert_thresholds:
      error_rate_percent: 5.0
      response_time_ms: 5000
      uptime_percent: 95.0

  # Security
  security:
    api_key_length: 64
    api_key_prefix: "gumroad_mcp_"
    max_request_size_mb: 10
    allowed_content_types:
      - "application/json"
      - "text/plain"
      - "application/x-www-form-urlencoded"
      - "multipart/form-data"

staging:
  # Gateway settings
  gateway:
    timeout: 30 # seconds
    open_timeout: 10 # seconds
    max_redirects: 3
    user_agent: "Gumroad-MCP-Gateway/1.0"

  # Health check settings
  health_check:
    interval: 120 # 2 minutes
    timeout: 10 # seconds
    retry_attempts: 3
    failure_threshold: 3 # consecutive failures before marking unhealthy
    success_threshold: 2 # consecutive successes before marking healthy

  # Rate limiting
  rate_limiting:
    global:
      unauthenticated_requests_per_minute: 20
      authenticated_requests_per_minute: 100
    default_limits:
      requests_per_hour: 1000
      requests_per_day: 10000
      requests_per_month: 100000
      burst_limit: 50
      concurrent_requests: 10

  # Pricing
  pricing:
    default_free_tier: 100
    minimum_per_request_price_cents: 1
    maximum_per_request_price_cents: 10000

  # Usage tracking
  usage_tracking:
    enabled: true
    batch_size: 100
    flush_interval: 60 # seconds
    retention_days: 90

  # Monitoring
  monitoring:
    enabled: true
    metrics_retention_days: 30
    alert_thresholds:
      error_rate_percent: 10.0 # More lenient for staging
      response_time_ms: 5000
      uptime_percent: 95.0

  # Security
  security:
    api_key_length: 64
    api_key_prefix: "gumroad_mcp_"
    max_request_size_mb: 10
    allowed_content_types:
      - "application/json"
      - "text/plain"
      - "application/x-www-form-urlencoded"
      - "multipart/form-data"

production:
  # Gateway settings
  gateway:
    timeout: 30 # seconds
    open_timeout: 10 # seconds
    max_redirects: 3
    user_agent: "Gumroad-MCP-Gateway/1.0"

  # Health check settings
  health_check:
    interval: 300 # 5 minutes
    timeout: 10 # seconds
    retry_attempts: 3
    failure_threshold: 3 # consecutive failures before marking unhealthy
    success_threshold: 2 # consecutive successes before marking healthy

  # Rate limiting
  rate_limiting:
    global:
      unauthenticated_requests_per_minute: 20
      authenticated_requests_per_minute: 100
    default_limits:
      requests_per_hour: 1000
      requests_per_day: 10000
      requests_per_month: 100000
      burst_limit: 50
      concurrent_requests: 10

  # Pricing
  pricing:
    default_free_tier: 100
    minimum_per_request_price_cents: 1
    maximum_per_request_price_cents: 10000

  # Usage tracking
  usage_tracking:
    enabled: true
    batch_size: 100
    flush_interval: 60 # seconds
    retention_days: 90

  # Monitoring
  monitoring:
    enabled: true
    metrics_retention_days: 30
    alert_thresholds:
      error_rate_percent: 5.0
      response_time_ms: 5000
      uptime_percent: 95.0

  # Security
  security:
    api_key_length: 64
    api_key_prefix: "gumroad_mcp_"
    max_request_size_mb: 10
    allowed_content_types:
      - "application/json"
      - "text/plain"
      - "application/x-www-form-urlencoded"
      - "multipart/form-data"
